/**
 * SAM - نظام إدارة شؤون الموظفين
 * Local Database Management using localStorage
 * إدارة قاعدة البيانات المحلية
 */

class Database {
    static init() {
        // Initialize database tables if they don't exist
        this.initializeTable('employees', []);
        this.initializeTable('attendance', []);
        this.initializeTable('leaves', []);
        this.initializeTable('payroll', []);
        this.initializeTable('contracts', []);
        this.initializeTable('advances', []);
        this.initializeTable('bonuses', []);
        this.initializeTable('penalties', []);
        this.initializeTable('departments', this.getDefaultDepartments());
        this.initializeTable('positions', this.getDefaultPositions());
        this.initializeTable('leave_types', this.getDefaultLeaveTypes());
        this.initializeTable('settings', this.getDefaultSettings());
        
        // Initialize sample data if needed
        this.initializeSampleData();
    }

    static initializeTable(tableName, defaultData = []) {
        const key = `sam_${tableName}`;
        if (!localStorage.getItem(key)) {
            localStorage.setItem(key, JSON.stringify(defaultData));
        }
    }

    static getDefaultDepartments() {
        return [
            { id: 'hr', name: 'الموارد البشرية', manager: null, created_at: new Date().toISOString() },
            { id: 'it', name: 'تقنية المعلومات', manager: null, created_at: new Date().toISOString() },
            { id: 'finance', name: 'المالية والمحاسبة', manager: null, created_at: new Date().toISOString() },
            { id: 'marketing', name: 'التسويق', manager: null, created_at: new Date().toISOString() },
            { id: 'operations', name: 'العمليات', manager: null, created_at: new Date().toISOString() }
        ];
    }

    static getDefaultPositions() {
        return [
            { id: 'manager', name: 'مدير', department: null, salary_range: { min: 8000, max: 15000 } },
            { id: 'supervisor', name: 'مشرف', department: null, salary_range: { min: 5000, max: 8000 } },
            { id: 'specialist', name: 'أخصائي', department: null, salary_range: { min: 4000, max: 7000 } },
            { id: 'employee', name: 'موظف', department: null, salary_range: { min: 3000, max: 5000 } },
            { id: 'intern', name: 'متدرب', department: null, salary_range: { min: 1500, max: 3000 } }
        ];
    }

    static getDefaultLeaveTypes() {
        return [
            { id: 'annual', name: 'إجازة سنوية', days_per_year: 30, paid: true, requires_approval: true },
            { id: 'sick', name: 'إجازة مرضية', days_per_year: 15, paid: true, requires_approval: false },
            { id: 'maternity', name: 'إجازة أمومة', days_per_year: 70, paid: true, requires_approval: true },
            { id: 'paternity', name: 'إجازة أبوة', days_per_year: 3, paid: true, requires_approval: true },
            { id: 'emergency', name: 'إجازة طارئة', days_per_year: 5, paid: true, requires_approval: true },
            { id: 'unpaid', name: 'إجازة بدون راتب', days_per_year: 0, paid: false, requires_approval: true }
        ];
    }

    static getDefaultSettings() {
        return {
            company: {
                name: 'شركة المثال',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '+966123456789',
                email: '<EMAIL>',
                website: '',
                registration_number: '',
                tax_number: '',
                currency: 'SAR',
                currency_symbol: 'ر.س',
                description: '',
                logo: null
            },
            working_hours: {
                start_time: '08:00',
                end_time: '17:00',
                break_duration: 60, // minutes
                working_days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
                working_days_per_month: 22 // عدد أيام العمل في الشهر
            },
            attendance: {
                late_threshold: 15, // minutes
                early_leave_threshold: 15, // minutes
                overtime_rate: 1.5,
                late_penalty_rate: 1 // how many hours to deduct for each hour of lateness
            },
            payroll: {
                social_insurance_rate: 0.09,
                social_insurance_enabled: false, // التأمينات الاجتماعية اختيارية
                income_tax_enabled: false, // ضريبة الدخل اختيارية
                tax_threshold: 3000,
                currency: 'SAR',
                pay_frequency: 'monthly',
                overtime_calculation: 'automatic',
                income_tax_brackets: [
                    { min: 0, max: 3000, rate: 0 },
                    { min: 3000, max: 5000, rate: 0.05 },
                    { min: 5000, max: 10000, rate: 0.10 },
                    { min: 10000, max: Infinity, rate: 0.15 }
                ]
            },
            leave: {
                annual_leave_days: 21,
                sick_leave_days: 30,
                maternity_leave_days: 70
            },
            notifications: {
                contract_expiry_days: 30,
                birthday_notifications: true,
                leave_approval_notifications: true
            }
        };
    }

    static initializeSampleData() {
        const employees = this.getEmployees();
        if (employees.length === 0) {
            // Add sample employees
            const sampleEmployees = [
                {
                    id: 'emp_001',
                    employee_number: '001',
                    name: 'أحمد محمد علي',
                    email: '<EMAIL>',
                    phone: '+966501234567',
                    national_id: '1234567890',
                    department: 'it',
                    position: 'manager',
                    hire_date: '2020-01-15',
                    birth_date: '1985-05-20',
                    address: 'الرياض، حي النخيل',
                    salary: 12000,
                    status: 'active',
                    photo: null,
                    documents: [],
                    created_at: new Date().toISOString()
                },
                {
                    id: 'emp_002',
                    employee_number: '002',
                    name: 'فاطمة أحمد السالم',
                    email: '<EMAIL>',
                    phone: '+966507654321',
                    national_id: '0987654321',
                    department: 'hr',
                    position: 'specialist',
                    hire_date: '2021-03-10',
                    birth_date: '1990-08-15',
                    address: 'الرياض، حي الملز',
                    salary: 6000,
                    status: 'active',
                    photo: null,
                    documents: [],
                    created_at: new Date().toISOString()
                }
            ];
            
            localStorage.setItem('sam_employees', JSON.stringify(sampleEmployees));
        }
    }

    // Generic CRUD operations
    static create(table, data) {
        const items = this.getAll(table);
        const newItem = {
            id: this.generateId(),
            ...data,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        items.push(newItem);
        localStorage.setItem(`sam_${table}`, JSON.stringify(items));
        return newItem;
    }

    static getAll(table) {
        const data = localStorage.getItem(`sam_${table}`);
        return data ? JSON.parse(data) : [];
    }

    static getById(table, id) {
        const items = this.getAll(table);
        return items.find(item => item.id === id);
    }

    static update(table, id, data) {
        const items = this.getAll(table);
        const index = items.findIndex(item => item.id === id);
        if (index === -1) {
            throw new Error('العنصر غير موجود');
        }
        
        items[index] = {
            ...items[index],
            ...data,
            updated_at: new Date().toISOString()
        };
        
        localStorage.setItem(`sam_${table}`, JSON.stringify(items));
        return items[index];
    }

    static delete(table, id) {
        const items = this.getAll(table);
        const filteredItems = items.filter(item => item.id !== id);
        if (filteredItems.length === items.length) {
            throw new Error('العنصر غير موجود');
        }
        localStorage.setItem(`sam_${table}`, JSON.stringify(filteredItems));
        return true;
    }

    // Employee specific methods
    static getEmployees() {
        return this.getAll('employees');
    }

    static getEmployee(id) {
        return this.getById('employees', id);
    }

    static createEmployee(data) {
        // Generate employee number if not provided
        if (!data.employee_number) {
            data.employee_number = this.generateEmployeeNumber();
        }
        return this.create('employees', data);
    }

    static updateEmployee(id, data) {
        return this.update('employees', id, data);
    }

    static deleteEmployee(id) {
        return this.delete('employees', id);
    }

    static generateEmployeeNumber() {
        const employees = this.getEmployees();
        const maxNumber = employees.reduce((max, emp) => {
            const num = parseInt(emp.employee_number);
            return num > max ? num : max;
        }, 0);
        return String(maxNumber + 1).padStart(3, '0');
    }

    // Attendance methods
    static getAttendance(filters = {}) {
        let attendance = this.getAll('attendance');
        
        if (filters.employee_id) {
            attendance = attendance.filter(a => a.employee_id === filters.employee_id);
        }
        
        if (filters.date) {
            attendance = attendance.filter(a => a.date === filters.date);
        }
        
        if (filters.month) {
            attendance = attendance.filter(a => a.date.startsWith(filters.month));
        }
        
        return attendance;
    }

    static getTodayAttendance() {
        const today = new Date().toISOString().split('T')[0];
        return this.getAttendance({ date: today });
    }

    static recordAttendance(employeeId, type, time = null) {
        const today = new Date().toISOString().split('T')[0];
        const currentTime = time || new Date().toTimeString().split(' ')[0];

        const existingRecord = this.getAttendance({ employee_id: employeeId, date: today })[0];

        if (existingRecord) {
            if (type === 'check_out') {
                const attendanceData = this.calculateAttendanceStatus(existingRecord.check_in, currentTime, employeeId);
                return this.update('attendance', existingRecord.id, {
                    check_out: currentTime,
                    status: attendanceData.status,
                    late_minutes: attendanceData.late_minutes,
                    early_leave_minutes: attendanceData.early_leave_minutes,
                    penalty_hours: attendanceData.penalty_hours,
                    updated_at: new Date().toISOString()
                });
            }
        } else if (type === 'check_in') {
            const attendanceData = this.calculateAttendanceStatus(currentTime, null, employeeId);
            return this.create('attendance', {
                employee_id: employeeId,
                date: today,
                check_in: currentTime,
                check_out: null,
                status: attendanceData.status,
                late_minutes: attendanceData.late_minutes,
                early_leave_minutes: attendanceData.early_leave_minutes,
                penalty_hours: attendanceData.penalty_hours,
                created_at: new Date().toISOString()
            });
        }

        throw new Error('لا يمكن تسجيل الحضور');
    }

    static calculateAttendanceStatus(checkIn, checkOut, employeeId) {
        const settings = this.getSettings();

        if (!employeeId) {
            // Fallback to global settings if no employee ID provided
            const startTime = settings.working_hours.start_time;
            const endTime = settings.working_hours.end_time;
            const lateThreshold = settings.attendance.late_threshold;
            const earlyLeaveThreshold = settings.attendance.early_leave_threshold;
            const latePenaltyRate = settings.attendance.late_penalty_rate || 1;

            if (!checkIn) return { status: 'absent', late_minutes: 0, early_leave_minutes: 0, penalty_hours: 0 };

            const checkInTime = new Date(`2000-01-01T${checkIn}`);
            const expectedStartTime = new Date(`2000-01-01T${startTime}`);

            let status = 'present';
            let lateMinutes = Math.max(0, (checkInTime - expectedStartTime) / (1000 * 60));
            let earlyLeaveMinutes = 0;

            // Check if late
            if (lateMinutes > lateThreshold) {
                status = 'late';
            }

            // Check early leave if checkout is provided
            if (checkOut) {
                const checkOutTime = new Date(`2000-01-01T${checkOut}`);
                const expectedEndTime = new Date(`2000-01-01T${endTime}`);
                earlyLeaveMinutes = Math.max(0, (expectedEndTime - checkOutTime) / (1000 * 60));

                if (earlyLeaveMinutes > earlyLeaveThreshold) {
                    status = status === 'late' ? 'late_early_leave' : 'early_leave';
                }
            }

            const penaltyHours = this.calculatePenaltyHours(lateMinutes, earlyLeaveMinutes, latePenaltyRate);

            return {
                status: status,
                late_minutes: Math.round(lateMinutes * 100) / 100,
                early_leave_minutes: Math.round(earlyLeaveMinutes * 100) / 100,
                penalty_hours: penaltyHours
            };
        }

        const employee = this.getEmployee(employeeId);
        if (!employee) return { status: 'absent', late_minutes: 0, early_leave_minutes: 0, penalty_hours: 0 };

        const startTime = employee.work_start_time || settings.working_hours.start_time;
        const endTime = employee.work_end_time || settings.working_hours.end_time;
        const lateThreshold = parseInt(employee.late_tolerance) || settings.attendance.late_threshold;
        const earlyLeaveThreshold = settings.attendance.early_leave_threshold;
        const latePenaltyRate = settings.attendance.late_penalty_rate || 1;

        if (!checkIn) return { status: 'absent', late_minutes: 0, early_leave_minutes: 0, penalty_hours: 0 };

        const checkInTime = new Date(`2000-01-01T${checkIn}`);
        const expectedStartTime = new Date(`2000-01-01T${startTime}`);

        let status = 'present';
        let lateMinutes = Math.max(0, (checkInTime - expectedStartTime) / (1000 * 60));
        let earlyLeaveMinutes = 0;

        // Check if late
        if (lateMinutes > lateThreshold) {
            status = 'late';
        }

        // Check early leave if checkout is provided
        if (checkOut) {
            const checkOutTime = new Date(`2000-01-01T${checkOut}`);
            const expectedEndTime = new Date(`2000-01-01T${endTime}`);
            earlyLeaveMinutes = Math.max(0, (expectedEndTime - checkOutTime) / (1000 * 60));

            if (earlyLeaveMinutes > earlyLeaveThreshold) {
                status = status === 'late' ? 'late_early_leave' : 'early_leave';
            }
        }

        const penaltyHours = this.calculatePenaltyHours(lateMinutes, earlyLeaveMinutes, latePenaltyRate);

        return {
            status: status,
            late_minutes: Math.round(lateMinutes * 100) / 100,
            early_leave_minutes: Math.round(earlyLeaveMinutes * 100) / 100,
            penalty_hours: penaltyHours
        };
    }

    static calculatePenaltyHours(lateMinutes, earlyLeaveMinutes, penaltyRate) {
        // تطبيق معدل الخصم على دقائق التأخير والانصراف المبكر
        const totalPenaltyMinutes = (lateMinutes + earlyLeaveMinutes) * penaltyRate;
        return Math.round((totalPenaltyMinutes / 60) * 100) / 100; // Convert to hours with 2 decimal places
    }

    /**
     * إعادة حساب بيانات الحضور للسجلات الموجودة
     */
    static recalculateAttendanceData() {
        try {
            const attendance = this.getAll('attendance') || [];
            let updated = 0;

            attendance.forEach(record => {
                if (record.check_in && !record.hasOwnProperty('late_minutes')) {
                    const attendanceData = this.calculateAttendanceStatus(
                        record.check_in,
                        record.check_out,
                        record.employee_id
                    );

                    this.update('attendance', record.id, {
                        ...record,
                        status: attendanceData.status,
                        late_minutes: attendanceData.late_minutes,
                        early_leave_minutes: attendanceData.early_leave_minutes,
                        penalty_hours: attendanceData.penalty_hours,
                        updated_at: new Date().toISOString()
                    });

                    updated++;
                }
            });

            console.log(`Updated ${updated} attendance records with detailed data`);
            return updated;
        } catch (error) {
            console.error('Error recalculating attendance data:', error);
            return 0;
        }
    }

    // Leave methods
    static getLeaves(filters = {}) {
        let leaves = this.getAll('leaves');
        
        if (filters.employee_id) {
            leaves = leaves.filter(l => l.employee_id === filters.employee_id);
        }
        
        if (filters.status) {
            leaves = leaves.filter(l => l.status === filters.status);
        }
        
        return leaves;
    }

    static getActiveLeaves() {
        const today = new Date().toISOString().split('T')[0];
        return this.getLeaves().filter(leave => 
            leave.status === 'approved' && 
            leave.start_date <= today && 
            leave.end_date >= today
        );
    }

    // Settings methods
    static getSettings() {
        try {
            const settings = localStorage.getItem('sam_settings');

            if (!settings) {
                console.log('No settings found, creating default settings');
                const defaultSettings = this.getDefaultSettings();
                this.saveSettings(defaultSettings);
                return defaultSettings;
            }

            const parsedSettings = JSON.parse(settings);

            // التحقق من صحة الإعدادات
            if (!this.validateSettings(parsedSettings)) {
                console.warn('Invalid settings detected, restoring from backup or defaults');

                // محاولة الاستعادة من النسخة الاحتياطية
                const backupSettings = this.restoreSettingsFromBackup();
                if (backupSettings && this.validateSettings(backupSettings)) {
                    return backupSettings;
                }

                // إذا فشلت الاستعادة، استخدم الإعدادات الافتراضية
                const defaultSettings = this.getDefaultSettings();
                this.saveSettings(defaultSettings);
                return defaultSettings;
            }

            // دمج الإعدادات مع الافتراضية لضمان وجود جميع الخصائص المطلوبة
            const defaultSettings = this.getDefaultSettings();
            const mergedSettings = this.mergeSettings(defaultSettings, parsedSettings);

            // حفظ الإعدادات المدموجة إذا كانت مختلفة
            if (JSON.stringify(mergedSettings) !== JSON.stringify(parsedSettings)) {
                this.saveSettings(mergedSettings);
            }

            return mergedSettings;

        } catch (error) {
            console.error('Error loading settings:', error);

            // محاولة الاستعادة من النسخة الاحتياطية
            const backupSettings = this.restoreSettingsFromBackup();
            if (backupSettings) {
                console.log('Settings restored from backup');
                return backupSettings;
            }

            // إذا فشل كل شيء، استخدم الإعدادات الافتراضية
            console.log('Using default settings as fallback');
            const defaultSettings = this.getDefaultSettings();

            try {
                this.saveSettings(defaultSettings);
            } catch (saveError) {
                console.error('Failed to save default settings:', saveError);
            }

            return defaultSettings;
        }
    }

    static validateSettings(settings) {
        if (!settings || typeof settings !== 'object') {
            return false;
        }

        // التحقق من وجود الأقسام الأساسية
        const requiredSections = ['company', 'working_hours', 'attendance', 'payroll'];
        for (const section of requiredSections) {
            if (!settings[section] || typeof settings[section] !== 'object') {
                console.warn(`Missing or invalid section: ${section}`);
                return false;
            }
        }

        // التحقق من الخصائص الأساسية في كل قسم
        if (!settings.company.name || !settings.working_hours.start_time ||
            !settings.working_hours.end_time || !settings.attendance.late_threshold) {
            console.warn('Missing required properties in settings');
            return false;
        }

        return true;
    }

    static mergeSettings(defaultSettings, userSettings) {
        const merged = JSON.parse(JSON.stringify(defaultSettings));

        // دمج عميق للإعدادات
        Object.keys(userSettings).forEach(key => {
            if (typeof userSettings[key] === 'object' && userSettings[key] !== null && !Array.isArray(userSettings[key])) {
                merged[key] = { ...merged[key], ...userSettings[key] };
            } else {
                merged[key] = userSettings[key];
            }
        });

        return merged;
    }

    static updateSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        localStorage.setItem('sam_settings', JSON.stringify(updatedSettings));
        return updatedSettings;
    }

    static saveSettings(settings) {
        try {
            // التحقق من صحة البيانات قبل الحفظ
            if (!settings || typeof settings !== 'object') {
                throw new Error('بيانات الإعدادات غير صالحة');
            }

            // إنشاء نسخة عميقة من الإعدادات لتجنب التعديل المباشر
            const settingsToSave = JSON.parse(JSON.stringify(settings));

            // إضافة طابع زمني للتحديث
            settingsToSave.last_updated = new Date().toISOString();
            settingsToSave.version = '1.0';

            // محاولة الحفظ مع التحقق من المساحة المتاحة
            const settingsString = JSON.stringify(settingsToSave);

            // التحقق من حجم البيانات
            if (settingsString.length > 5 * 1024 * 1024) { // 5MB limit
                throw new Error('حجم الإعدادات كبير جداً');
            }

            // حفظ الإعدادات
            localStorage.setItem('sam_settings', settingsString);

            // التحقق من نجاح الحفظ
            const savedSettings = localStorage.getItem('sam_settings');
            if (!savedSettings || savedSettings !== settingsString) {
                throw new Error('فشل في التحقق من حفظ الإعدادات');
            }

            // إنشاء نسخة احتياطية من الإعدادات
            this.createSettingsBackup(settingsToSave);

            console.log('Settings saved successfully at:', settingsToSave.last_updated);
            return true;

        } catch (error) {
            console.error('Error saving settings:', error);

            // محاولة استرداد الإعدادات من النسخة الاحتياطية
            if (error.name === 'QuotaExceededError') {
                this.handleStorageQuotaExceeded();
                throw new Error('مساحة التخزين ممتلئة. يرجى حذف بعض البيانات القديمة');
            }

            throw new Error('فشل في حفظ الإعدادات: ' + error.message);
        }
    }

    static createSettingsBackup(settings) {
        try {
            // الاحتفاظ بآخر 3 نسخ احتياطية من الإعدادات
            const backupKey = `sam_settings_backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(settings));

            // حذف النسخ الاحتياطية القديمة
            this.cleanupSettingsBackups();
        } catch (error) {
            console.warn('Failed to create settings backup:', error);
        }
    }

    static cleanupSettingsBackups() {
        try {
            const backupKeys = Object.keys(localStorage)
                .filter(key => key.startsWith('sam_settings_backup_'))
                .sort()
                .reverse(); // الأحدث أولاً

            // الاحتفاظ بآخر 3 نسخ احتياطية فقط
            if (backupKeys.length > 3) {
                backupKeys.slice(3).forEach(key => {
                    localStorage.removeItem(key);
                });
            }
        } catch (error) {
            console.warn('Failed to cleanup settings backups:', error);
        }
    }

    static restoreSettingsFromBackup() {
        try {
            const backupKeys = Object.keys(localStorage)
                .filter(key => key.startsWith('sam_settings_backup_'))
                .sort()
                .reverse();

            if (backupKeys.length > 0) {
                const latestBackup = localStorage.getItem(backupKeys[0]);
                if (latestBackup) {
                    const settings = JSON.parse(latestBackup);
                    localStorage.setItem('sam_settings', JSON.stringify(settings));
                    console.log('Settings restored from backup');
                    return settings;
                }
            }

            return null;
        } catch (error) {
            console.error('Failed to restore settings from backup:', error);
            return null;
        }
    }

    static handleStorageQuotaExceeded() {
        try {
            // حذف البيانات القديمة غير المهمة
            const keysToClean = Object.keys(localStorage).filter(key =>
                key.startsWith('sam_') &&
                (key.includes('_temp_') || key.includes('_cache_'))
            );

            keysToClean.forEach(key => {
                localStorage.removeItem(key);
            });

            console.log('Cleaned up temporary storage data');
        } catch (error) {
            console.error('Failed to handle storage quota exceeded:', error);
        }
    }

    // Utility methods
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    static backup() {
        const data = {};
        const tables = ['employees', 'attendance', 'leaves', 'payroll', 'contracts', 
                      'advances', 'bonuses', 'penalties', 'departments', 'positions', 
                      'leave_types', 'settings', 'users'];
        
        tables.forEach(table => {
            data[table] = this.getAll(table);
        });
        
        data.backup_date = new Date().toISOString();
        data.version = '1.0';
        
        return JSON.stringify(data, null, 2);
    }

    static restore(backupData) {
        try {
            const data = JSON.parse(backupData);
            
            Object.keys(data).forEach(key => {
                if (key !== 'backup_date' && key !== 'version') {
                    localStorage.setItem(`sam_${key}`, JSON.stringify(data[key]));
                }
            });
            
            return true;
        } catch (error) {
            throw new Error('ملف النسخة الاحتياطية غير صالح');
        }
    }

    static clearAll() {
        const keys = Object.keys(localStorage).filter(key => key.startsWith('sam_'));
        keys.forEach(key => localStorage.removeItem(key));
    }

    // Search methods
    static searchEmployees(query) {
        const employees = this.getEmployees();
        const searchTerm = query.toLowerCase();
        
        return employees.filter(emp => 
            emp.name.toLowerCase().includes(searchTerm) ||
            emp.employee_number.includes(searchTerm) ||
            emp.email.toLowerCase().includes(searchTerm) ||
            emp.phone.includes(searchTerm) ||
            emp.national_id.includes(searchTerm)
        );
    }

    static getStatistics() {
        const employees = this.getEmployees();
        const todayAttendance = this.getTodayAttendance();
        const activeLeaves = this.getActiveLeaves();

        return {
            total_employees: employees.length,
            active_employees: employees.filter(e => e.status === 'active').length,
            present_today: todayAttendance.filter(a => a.status === 'present').length,
            late_today: todayAttendance.filter(a => a.status === 'late').length,
            on_leave: activeLeaves.length,
            departments: this.getAll('departments').length
        };
    }

    static clearTable(tableName) {
        localStorage.removeItem(`sam_${tableName}`);
    }

    static resetSettings() {
        const defaultSettings = {
            company: {
                name: 'شركة نموذجية',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '+966 11 123 4567',
                email: '<EMAIL>'
            },
            working_hours: {
                start_time: '08:00',
                end_time: '17:00',
                break_duration: 60,
                working_days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday']
            },
            attendance: {
                late_threshold: 15,
                early_leave_threshold: 15,
                overtime_rate: 1.5,
                late_penalty_rate: 1
            },
            leave: {
                annual_leave_days: 21,
                sick_leave_days: 30,
                maternity_leave_days: 70
            },
            payroll: {
                social_insurance_rate: 0.09,
                income_tax_brackets: [
                    { min: 0, max: 3000, rate: 0 },
                    { min: 3000, max: 5000, rate: 0.05 },
                    { min: 5000, max: 10000, rate: 0.10 },
                    { min: 10000, max: Infinity, rate: 0.15 }
                ]
            }
        };

        localStorage.setItem('sam_settings', JSON.stringify(defaultSettings));
        return defaultSettings;
    }
}

// Initialize database when script loads
Database.init();
