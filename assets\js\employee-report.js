/**
 * SAM - نظام إدارة شؤون الموظفين
 * Employee Detailed Report Module
 * وحدة التقرير المفصل للموظف
 */

class EmployeeReportManager {
    constructor() {
        this.currentYear = new Date().getFullYear();
        this.selectedEmployeeId = null;
    }

    render() {
        if (!window.authManager.hasPermission('reports')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadEmployees();
    }

    getMainHTML() {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-user-chart me-2"></i>التقرير المفصل للموظف</h2>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" id="generateReportBtn" disabled>
                                    <i class="fas fa-chart-line me-2"></i>إنشاء التقرير
                                </button>
                                <button type="button" class="btn btn-success" id="exportReportBtn" disabled>
                                    <i class="fas fa-download me-2"></i>تصدير PDF
                                </button>
                            </div>
                        </div>

                        <!-- اختيار الموظف والفترة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">اختيار الموظف والفترة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" id="employeeSelect">
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">السنة</label>
                                        <select class="form-select" id="yearSelect">
                                            <option value="${this.currentYear}">${this.currentYear}</option>
                                            <option value="${this.currentYear - 1}">${this.currentYear - 1}</option>
                                            <option value="${this.currentYear - 2}">${this.currentYear - 2}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">من شهر</label>
                                        <select class="form-select" id="fromMonthSelect">
                                            <option value="01">يناير</option>
                                            <option value="02">فبراير</option>
                                            <option value="03">مارس</option>
                                            <option value="04">أبريل</option>
                                            <option value="05">مايو</option>
                                            <option value="06">يونيو</option>
                                            <option value="07">يوليو</option>
                                            <option value="08">أغسطس</option>
                                            <option value="09">سبتمبر</option>
                                            <option value="10">أكتوبر</option>
                                            <option value="11">نوفمبر</option>
                                            <option value="12">ديسمبر</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">إلى شهر</label>
                                        <select class="form-select" id="toMonthSelect">
                                            <option value="01">يناير</option>
                                            <option value="02">فبراير</option>
                                            <option value="03">مارس</option>
                                            <option value="04">أبريل</option>
                                            <option value="05">مايو</option>
                                            <option value="06">يونيو</option>
                                            <option value="07">يوليو</option>
                                            <option value="08">أغسطس</option>
                                            <option value="09">سبتمبر</option>
                                            <option value="10">أكتوبر</option>
                                            <option value="11">نوفمبر</option>
                                            <option value="12">ديسمبر</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة التقرير -->
                        <div id="reportArea" style="display: none;">
                            <!-- معلومات الموظف -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">معلومات الموظف</h5>
                                </div>
                                <div class="card-body" id="employeeInfo">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>

                            <!-- ملخص الحضور -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">ملخص الحضور والدوام</h5>
                                </div>
                                <div class="card-body" id="attendanceSummary">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>

                            <!-- تفاصيل الإجازات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">تفاصيل الإجازات</h5>
                                </div>
                                <div class="card-body" id="leaveDetails">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>

                            <!-- السلف والقروض -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">السلف والقروض</h5>
                                </div>
                                <div class="card-body" id="advancesDetails">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>

                            <!-- ملخص الرواتب -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">ملخص الرواتب</h5>
                                </div>
                                <div class="card-body" id="payrollSummary">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>

                            <!-- الجزاءات والمكافآت -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">الجزاءات والمكافآت</h5>
                                </div>
                                <div class="card-body" id="penaltiesRewards">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        document.getElementById('employeeSelect').addEventListener('change', (e) => {
            this.selectedEmployeeId = e.target.value;
            const generateBtn = document.getElementById('generateReportBtn');
            const exportBtn = document.getElementById('exportReportBtn');
            
            if (this.selectedEmployeeId) {
                generateBtn.disabled = false;
                exportBtn.disabled = false;
            } else {
                generateBtn.disabled = true;
                exportBtn.disabled = true;
                document.getElementById('reportArea').style.display = 'none';
            }
        });

        document.getElementById('generateReportBtn').addEventListener('click', () => {
            this.generateReport();
        });

        document.getElementById('exportReportBtn').addEventListener('click', () => {
            this.exportToPDF();
        });
    }

    loadEmployees() {
        const employees = Database.getEmployees();
        const select = document.getElementById('employeeSelect');

        select.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            select.innerHTML += `<option value="${employee.id}">${employee.name} - ${employee.employee_number}</option>`;
        });
    }

    /**
     * إنشاء التقرير المفصل
     */
    generateReport() {
        if (!this.selectedEmployeeId) {
            window.samApp.showAlert('يرجى اختيار موظف أولاً', 'warning');
            return;
        }

        const employee = Database.getEmployee(this.selectedEmployeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }

        const year = document.getElementById('yearSelect').value;
        const fromMonth = document.getElementById('fromMonthSelect').value;
        const toMonth = document.getElementById('toMonthSelect').value;

        try {
            // عرض منطقة التقرير
            document.getElementById('reportArea').style.display = 'block';

            // إنشاء أقسام التقرير
            this.generateEmployeeInfo(employee);
            this.generateAttendanceSummary(employee.id, year, fromMonth, toMonth);
            this.generateLeaveDetails(employee.id, year, fromMonth, toMonth);
            this.generateAdvancesDetails(employee.id, year, fromMonth, toMonth);
            this.generatePayrollSummary(employee.id, year, fromMonth, toMonth);
            this.generatePenaltiesRewards(employee.id, year, fromMonth, toMonth);

            window.samApp.showAlert('تم إنشاء التقرير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير:', error);
            window.samApp.showAlert('حدث خطأ في إنشاء التقرير: ' + error.message, 'danger');
        }
    }

    /**
     * معلومات الموظف
     */
    generateEmployeeInfo(employee) {
        const container = document.getElementById('employeeInfo');

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>الاسم:</strong></td><td>${employee.name}</td></tr>
                        <tr><td><strong>رقم الموظف:</strong></td><td>${employee.employee_number}</td></tr>
                        <tr><td><strong>المنصب:</strong></td><td>${employee.position || 'غير محدد'}</td></tr>
                        <tr><td><strong>القسم:</strong></td><td>${employee.department || 'غير محدد'}</td></tr>
                        <tr><td><strong>تاريخ التوظيف:</strong></td><td>${employee.hire_date || 'غير محدد'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>الراتب الأساسي:</strong></td><td>${window.samApp.formatCurrency(employee.salary || 0)}</td></tr>
                        <tr><td><strong>بدل السكن:</strong></td><td>${window.samApp.formatCurrency(employee.housing_allowance || 0)}</td></tr>
                        <tr><td><strong>بدل المواصلات:</strong></td><td>${window.samApp.formatCurrency(employee.transport_allowance || 0)}</td></tr>
                        <tr><td><strong>الهاتف:</strong></td><td>${employee.phone || 'غير محدد'}</td></tr>
                        <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${employee.status === 'active' ? 'success' : 'danger'}">${employee.status === 'active' ? 'نشط' : 'غير نشط'}</span></td></tr>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * ملخص الحضور والدوام
     */
    generateAttendanceSummary(employeeId, year, fromMonth, toMonth) {
        const container = document.getElementById('attendanceSummary');

        // جلب بيانات الحضور للفترة المحددة
        const attendance = Database.getAll('attendance') || [];
        const periodAttendance = attendance.filter(record => {
            if (record.employee_id !== employeeId || !record.date) return false;

            const recordDate = new Date(record.date);
            const recordYear = recordDate.getFullYear().toString();
            const recordMonth = (recordDate.getMonth() + 1).toString().padStart(2, '0');

            return recordYear === year && recordMonth >= fromMonth && recordMonth <= toMonth;
        });

        // حساب الإحصائيات
        const totalDays = periodAttendance.length;
        const presentDays = periodAttendance.filter(r =>
            r.status === 'present' || r.status === 'late' || r.status === 'early_leave' || r.status === 'late_early_leave'
        ).length;
        const absentDays = periodAttendance.filter(r => r.status === 'absent').length;
        const lateDays = periodAttendance.filter(r =>
            r.status === 'late' || r.status === 'late_early_leave'
        ).length;
        const earlyLeaveDays = periodAttendance.filter(r =>
            r.status === 'early_leave' || r.status === 'late_early_leave'
        ).length;

        // حساب إجمالي ساعات التأخير
        const totalLateHours = periodAttendance.reduce((sum, record) => {
            return sum + (parseFloat(record.penalty_hours) || 0);
        }, 0);

        const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;

        container.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${totalDays}</h3>
                            <p class="mb-0">إجمالي الأيام</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${presentDays}</h3>
                            <p class="mb-0">أيام الحضور</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>${absentDays}</h3>
                            <p class="mb-0">أيام الغياب</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${attendancePercentage}%</h3>
                            <p class="mb-0">نسبة الحضور</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${lateDays}</h4>
                            <p class="mb-0">أيام التأخير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4>${earlyLeaveDays}</h4>
                            <p class="mb-0">أيام الانصراف المبكر</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h4>${totalLateHours.toFixed(2)}</h4>
                            <p class="mb-0">إجمالي ساعات الخصم</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * تفاصيل الإجازات
     */
    generateLeaveDetails(employeeId, year, fromMonth, toMonth) {
        const container = document.getElementById('leaveDetails');

        // جلب بيانات الإجازات للفترة المحددة
        const leaves = Database.getAll('leaves') || [];
        const periodLeaves = leaves.filter(leave => {
            if (leave.employee_id !== employeeId || !leave.start_date) return false;

            const leaveDate = new Date(leave.start_date);
            const leaveYear = leaveDate.getFullYear().toString();
            const leaveMonth = (leaveDate.getMonth() + 1).toString().padStart(2, '0');

            return leaveYear === year && leaveMonth >= fromMonth && leaveMonth <= toMonth;
        });

        // تجميع الإجازات حسب النوع
        const leaveTypes = {};
        let totalLeaveDays = 0;

        periodLeaves.forEach(leave => {
            const type = leave.type || 'غير محدد';
            const days = parseInt(leave.days) || 0;

            if (!leaveTypes[type]) {
                leaveTypes[type] = { count: 0, days: 0, leaves: [] };
            }

            leaveTypes[type].count++;
            leaveTypes[type].days += days;
            leaveTypes[type].leaves.push(leave);
            totalLeaveDays += days;
        });

        let leaveTypesHTML = '';
        Object.keys(leaveTypes).forEach(type => {
            const data = leaveTypes[type];
            leaveTypesHTML += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">${type}</h6>
                            <p class="card-text">
                                <strong>عدد الإجازات:</strong> ${data.count}<br>
                                <strong>إجمالي الأيام:</strong> ${data.days}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });

        // جدول تفاصيل الإجازات
        let leavesTableHTML = '';
        if (periodLeaves.length > 0) {
            leavesTableHTML = `
                <div class="table-responsive mt-3">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>عدد الأيام</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            periodLeaves.forEach(leave => {
                const statusBadge = this.getLeaveStatusBadge(leave.status);
                leavesTableHTML += `
                    <tr>
                        <td>${leave.type || 'غير محدد'}</td>
                        <td>${leave.start_date}</td>
                        <td>${leave.end_date}</td>
                        <td class="text-center">${leave.days}</td>
                        <td>${leave.reason || 'غير محدد'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            leavesTableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <strong>إجمالي أيام الإجازات في الفترة المحددة:</strong> ${totalLeaveDays} يوم
                    </div>
                </div>
            </div>
            <div class="row">
                ${leaveTypesHTML || '<div class="col-12"><p class="text-muted">لا توجد إجازات في الفترة المحددة</p></div>'}
            </div>
            ${leavesTableHTML}
        `;
    }

    /**
     * تفاصيل السلف والقروض
     */
    generateAdvancesDetails(employeeId, year, fromMonth, toMonth) {
        const container = document.getElementById('advancesDetails');

        // جلب بيانات السلف للموظف
        const advances = Database.getAll('advances') || [];
        const employeeAdvances = advances.filter(advance => advance.employee_id === employeeId);

        let totalAdvances = 0;
        let totalPaid = 0;
        let totalRemaining = 0;

        let advancesTableHTML = '';
        if (employeeAdvances.length > 0) {
            advancesTableHTML = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>تاريخ السلفة</th>
                                <th>المبلغ</th>
                                <th>القسط الشهري</th>
                                <th>المبلغ المدفوع</th>
                                <th>المبلغ المتبقي</th>
                                <th>الحالة</th>
                                <th>السبب</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            employeeAdvances.forEach(advance => {
                const amount = parseFloat(advance.amount) || 0;
                const paidAmount = parseFloat(advance.paid_amount) || 0;
                const remainingAmount = parseFloat(advance.remaining_amount) || 0;
                const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;

                totalAdvances += amount;
                totalPaid += paidAmount;
                totalRemaining += remainingAmount;

                const statusBadge = this.getAdvanceStatusBadge(advance.status);

                advancesTableHTML += `
                    <tr>
                        <td>${advance.request_date || 'غير محدد'}</td>
                        <td>${window.samApp.formatCurrency(amount)}</td>
                        <td>${window.samApp.formatCurrency(monthlyInstallment)}</td>
                        <td>${window.samApp.formatCurrency(paidAmount)}</td>
                        <td>${window.samApp.formatCurrency(remainingAmount)}</td>
                        <td>${statusBadge}</td>
                        <td>${advance.reason || 'غير محدد'}</td>
                    </tr>
                `;
            });

            advancesTableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalAdvances)}</h4>
                            <p class="mb-0">إجمالي السلف</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalPaid)}</h4>
                            <p class="mb-0">المبلغ المدفوع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalRemaining)}</h4>
                            <p class="mb-0">المبلغ المتبقي</p>
                        </div>
                    </div>
                </div>
            </div>
            ${advancesTableHTML || '<p class="text-muted">لا توجد سلف مسجلة لهذا الموظف</p>'}
        `;
    }

    /**
     * ملخص الرواتب
     */
    generatePayrollSummary(employeeId, year, fromMonth, toMonth) {
        const container = document.getElementById('payrollSummary');

        // جلب بيانات الرواتب للفترة المحددة
        const payrolls = Database.getAll('payroll') || [];
        const periodPayrolls = payrolls.filter(payroll => {
            if (payroll.employee_id !== employeeId || !payroll.month) return false;

            const [payrollYear, payrollMonth] = payroll.month.split('-');
            return payrollYear === year && payrollMonth >= fromMonth && payrollMonth <= toMonth;
        });

        // حساب الإجماليات
        let totalEarnedSalary = 0;
        let totalAllowances = 0;
        let totalDeductions = 0;
        let totalNetSalary = 0;

        periodPayrolls.forEach(payroll => {
            totalEarnedSalary += parseFloat(payroll.earned_salary) || 0;
            totalAllowances += parseFloat(payroll.total_allowances) || 0;
            totalDeductions += parseFloat(payroll.total_deductions) || 0;
            totalNetSalary += parseFloat(payroll.net_salary) || 0;
        });

        // جدول تفاصيل الرواتب
        let payrollTableHTML = '';
        if (periodPayrolls.length > 0) {
            payrollTableHTML = `
                <div class="table-responsive mt-3">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>أيام الحضور</th>
                                <th>الراتب المستحق</th>
                                <th>البدلات</th>
                                <th>الخصومات</th>
                                <th>صافي الراتب</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            periodPayrolls.forEach(payroll => {
                const statusBadge = this.getPayrollStatusBadge(payroll.status);
                payrollTableHTML += `
                    <tr>
                        <td>${payroll.month}</td>
                        <td class="text-center">${payroll.actual_working_days}/${payroll.total_working_days}</td>
                        <td>${window.samApp.formatCurrency(payroll.earned_salary || 0)}</td>
                        <td>${window.samApp.formatCurrency(payroll.total_allowances || 0)}</td>
                        <td>${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                        <td class="fw-bold">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            payrollTableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalEarnedSalary)}</h4>
                            <p class="mb-0">إجمالي الراتب المستحق</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalAllowances)}</h4>
                            <p class="mb-0">إجمالي البدلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalDeductions)}</h4>
                            <p class="mb-0">إجمالي الخصومات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalNetSalary)}</h4>
                            <p class="mb-0">إجمالي صافي الراتب</p>
                        </div>
                    </div>
                </div>
            </div>
            ${payrollTableHTML || '<p class="text-muted">لا توجد كشوف رواتب في الفترة المحددة</p>'}
        `;
    }

    /**
     * الجزاءات والمكافآت
     */
    generatePenaltiesRewards(employeeId, year, fromMonth, toMonth) {
        const container = document.getElementById('penaltiesRewards');

        // جلب بيانات الجزاءات (إذا كانت موجودة)
        const penalties = Database.getAll('penalties') || [];
        const employeePenalties = penalties.filter(penalty => penalty.employee_id === employeeId);

        // جلب بيانات المكافآت (إذا كانت موجودة)
        const rewards = Database.getAll('rewards') || [];
        const employeeRewards = rewards.filter(reward => reward.employee_id === employeeId);

        let totalPenalties = 0;
        let totalRewards = 0;

        // جدول الجزاءات
        let penaltiesHTML = '';
        if (employeePenalties.length > 0) {
            penaltiesHTML = `
                <h6>الجزاءات</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            employeePenalties.forEach(penalty => {
                const amount = parseFloat(penalty.amount) || 0;
                totalPenalties += amount;

                penaltiesHTML += `
                    <tr>
                        <td>${penalty.date || 'غير محدد'}</td>
                        <td>${penalty.type || 'غير محدد'}</td>
                        <td>${window.samApp.formatCurrency(amount)}</td>
                        <td>${penalty.reason || 'غير محدد'}</td>
                        <td><span class="badge bg-${penalty.status === 'approved' ? 'success' : 'warning'}">${penalty.status === 'approved' ? 'مطبق' : 'في الانتظار'}</span></td>
                    </tr>
                `;
            });

            penaltiesHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        // جدول المكافآت
        let rewardsHTML = '';
        if (employeeRewards.length > 0) {
            rewardsHTML = `
                <h6 class="mt-3">المكافآت</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            employeeRewards.forEach(reward => {
                const amount = parseFloat(reward.amount) || 0;
                totalRewards += amount;

                rewardsHTML += `
                    <tr>
                        <td>${reward.date || 'غير محدد'}</td>
                        <td>${reward.type || 'غير محدد'}</td>
                        <td>${window.samApp.formatCurrency(amount)}</td>
                        <td>${reward.reason || 'غير محدد'}</td>
                        <td><span class="badge bg-${reward.status === 'approved' ? 'success' : 'warning'}">${reward.status === 'approved' ? 'مطبق' : 'في الانتظار'}</span></td>
                    </tr>
                `;
            });

            rewardsHTML += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalPenalties)}</h4>
                            <p class="mb-0">إجمالي الجزاءات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${window.samApp.formatCurrency(totalRewards)}</h4>
                            <p class="mb-0">إجمالي المكافآت</p>
                        </div>
                    </div>
                </div>
            </div>
            ${penaltiesHTML || '<p class="text-muted">لا توجد جزاءات مسجلة</p>'}
            ${rewardsHTML || '<p class="text-muted">لا توجد مكافآت مسجلة</p>'}
        `;
    }

    /**
     * الدوال المساعدة للحصول على شارات الحالة
     */
    getLeaveStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'approved': '<span class="badge bg-success">موافق عليها</span>',
            'rejected': '<span class="badge bg-danger">مرفوضة</span>',
            'cancelled': '<span class="badge bg-secondary">ملغية</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    getAdvanceStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'approved': '<span class="badge bg-success">موافق عليها</span>',
            'rejected': '<span class="badge bg-danger">مرفوضة</span>',
            'completed': '<span class="badge bg-info">مكتملة</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    getPayrollStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'paid': '<span class="badge bg-success">مدفوع</span>',
            'cancelled': '<span class="badge bg-danger">ملغي</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    /**
     * تصدير التقرير إلى PDF
     */
    exportToPDF() {
        if (!this.selectedEmployeeId) {
            window.samApp.showAlert('يرجى إنشاء التقرير أولاً', 'warning');
            return;
        }

        try {
            // إخفاء الأزرار مؤقتاً للطباعة
            const buttons = document.querySelectorAll('.btn-group');
            buttons.forEach(btn => btn.style.display = 'none');

            // إضافة عنوان للطباعة
            const employee = Database.getEmployee(this.selectedEmployeeId);
            const year = document.getElementById('yearSelect').value;
            const fromMonth = document.getElementById('fromMonthSelect').value;
            const toMonth = document.getElementById('toMonthSelect').value;

            const printTitle = document.createElement('div');
            printTitle.id = 'printTitle';
            printTitle.innerHTML = `
                <div class="text-center mb-4" style="page-break-inside: avoid;">
                    <h1>التقرير المفصل للموظف</h1>
                    <h3>${employee.name} - ${employee.employee_number}</h3>
                    <p>الفترة: من ${fromMonth}/${year} إلى ${toMonth}/${year}</p>
                    <hr>
                </div>
            `;

            document.getElementById('reportArea').insertBefore(printTitle, document.getElementById('reportArea').firstChild);

            // طباعة الصفحة
            window.print();

            // إعادة إظهار الأزرار وحذف العنوان
            setTimeout(() => {
                buttons.forEach(btn => btn.style.display = '');
                const titleElement = document.getElementById('printTitle');
                if (titleElement) {
                    titleElement.remove();
                }
            }, 1000);

            window.samApp.showAlert('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            window.samApp.showAlert('حدث خطأ في تصدير التقرير: ' + error.message, 'danger');
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.EmployeeReportManager = EmployeeReportManager;
