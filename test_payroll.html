<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الرواتب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; text-align: right; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">اختبار نظام الرواتب المطور</h1>
        
        <div class="test-section">
            <h3>اختبار حساب ساعات التأخير</h3>
            <button class="btn btn-primary" onclick="testLateHoursCalculation()">تشغيل الاختبار</button>
            <div id="lateHoursResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار حساب الراتب بناءً على الحضور</h3>
            <button class="btn btn-primary" onclick="testSalaryCalculation()">تشغيل الاختبار</button>
            <div id="salaryResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار تطبيق معدل الخصم</h3>
            <button class="btn btn-primary" onclick="testPenaltyRate()">تشغيل الاختبار</button>
            <div id="penaltyResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار إنشاء كشف راتب</h3>
            <button class="btn btn-primary" onclick="testPayrollGeneration()">تشغيل الاختبار</button>
            <div id="payrollResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الإعدادات الجديدة</h3>
            <button class="btn btn-primary" onclick="testNewSettings()">تشغيل الاختبار</button>
            <div id="settingsResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار حساب ساعات الخصم</h3>
            <button class="btn btn-primary" onclick="testPenaltyHours()">تشغيل الاختبار</button>
            <div id="penaltyResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار تفاصيل البدلات والخصومات</h3>
            <button class="btn btn-primary" onclick="testDetailedCalculations()">تشغيل الاختبار</button>
            <div id="detailsResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الخصومات الشاملة</h3>
            <button class="btn btn-primary" onclick="testComprehensiveDeductions()">تشغيل الاختبار</button>
            <div id="comprehensiveResult"></div>
        </div>
    </div>

    <script src="assets/js/database.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/payroll.js"></script>
    <script>
        // إنشاء بيانات اختبار
        function setupTestData() {
            // إنشاء موظف اختبار
            const testEmployee = {
                id: 'test_emp_001',
                name: 'أحمد محمد',
                employee_number: 'EMP001',
                salary: 5000,
                status: 'active',
                work_start_time: '08:00',
                work_end_time: '17:00'
            };
            
            Database.create('employees', testEmployee);
            
            // إنشاء سجلات حضور اختبار
            const testAttendance = [
                {
                    employee_id: 'test_emp_001',
                    date: '2024-01-01',
                    check_in: '08:30', // تأخير 30 دقيقة
                    check_out: '17:00',
                    status: 'late'
                },
                {
                    employee_id: 'test_emp_001',
                    date: '2024-01-02',
                    check_in: '08:00',
                    check_out: '16:30', // انصراف مبكر 30 دقيقة
                    status: 'early_leave'
                },
                {
                    employee_id: 'test_emp_001',
                    date: '2024-01-03',
                    check_in: '08:45', // تأخير 45 دقيقة
                    check_out: '16:45', // انصراف مبكر 15 دقيقة
                    status: 'late_early_leave'
                }
            ];
            
            testAttendance.forEach(record => {
                Database.create('attendance', record);
            });
            
            return testEmployee;
        }

        function testLateHoursCalculation() {
            const resultDiv = document.getElementById('lateHoursResult');
            try {
                const testEmployee = setupTestData();
                const payrollManager = new PayrollManager();
                
                // اختبار حساب ساعات التأخير
                const attendanceData = payrollManager.calculateDetailedAttendance('test_emp_001', '2024-01');
                
                const expectedLateHours = (30 + 45) / 60; // 1.25 ساعة تأخير
                const actualLateHours = attendanceData.totalLateHours;
                
                let result = `<div class="test-result ${Math.abs(expectedLateHours - actualLateHours) < 0.1 ? 'test-pass' : 'test-fail'}">`;
                result += `<strong>نتيجة الاختبار:</strong><br>`;
                result += `ساعات التأخير المتوقعة: ${expectedLateHours.toFixed(2)}<br>`;
                result += `ساعات التأخير الفعلية: ${actualLateHours.toFixed(2)}<br>`;
                result += `ساعات الخصم: ${attendanceData.penaltyHours.toFixed(2)}<br>`;
                result += `الحالة: ${Math.abs(expectedLateHours - actualLateHours) < 0.1 ? 'نجح' : 'فشل'}`;
                result += `</div>`;
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testSalaryCalculation() {
            const resultDiv = document.getElementById('salaryResult');
            try {
                const payrollManager = new PayrollManager();
                const testEmployee = Database.getEmployee('test_emp_001');

                if (!testEmployee) {
                    setupTestData();
                }

                // اختبار حساب الراتب الجديد المبسط
                const attendanceData = payrollManager.getEmployeeAttendance('test_emp_001', '2024-01');
                const fullSalary = parseFloat(testEmployee.salary) || 0;
                const earnedSalary = payrollManager.calculateEarnedSalary(fullSalary, attendanceData);
                const allowances = payrollManager.calculateAllowances(testEmployee, attendanceData);
                const deductions = payrollManager.calculateDeductions(testEmployee, earnedSalary, '2024-01');

                let result = `<div class="test-result test-pass">`;
                result += `<strong>نتيجة حساب الراتب المبسط:</strong><br>`;
                result += `الراتب الأساسي الكامل: ${fullSalary.toFixed(2)}<br>`;
                result += `أيام الحضور الفعلية: ${attendanceData.actualDays}<br>`;
                result += `أيام العمل الكاملة: ${attendanceData.totalDays}<br>`;
                result += `الراتب المستحق: ${earnedSalary.toFixed(2)}<br>`;
                result += `البدلات: ${allowances.toFixed(2)}<br>`;
                result += `الخصومات: ${deductions.toFixed(2)}<br>`;
                result += `صافي الراتب: ${Math.max(0, earnedSalary + allowances - deductions).toFixed(2)}<br>`;
                result += `نسبة الحضور: ${attendanceData.percentage}%`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testPenaltyRate() {
            const resultDiv = document.getElementById('penaltyResult');
            try {
                const settings = Database.getSettings();
                const penaltyRate = settings.attendance.late_penalty_rate || 1;
                
                // اختبار تطبيق معدل الخصم
                const lateMinutes = 60; // ساعة واحدة تأخير
                const penaltyHours = Database.calculatePenaltyHours(lateMinutes, 0, penaltyRate);
                
                let result = `<div class="test-result test-pass">`;
                result += `<strong>نتيجة اختبار معدل الخصم:</strong><br>`;
                result += `دقائق التأخير: ${lateMinutes}<br>`;
                result += `معدل الخصم: ${penaltyRate}x<br>`;
                result += `ساعات الخصم المحسوبة: ${penaltyHours}<br>`;
                result += `النتيجة المتوقعة: ${(lateMinutes * penaltyRate / 60).toFixed(2)}`;
                result += `</div>`;
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testPayrollGeneration() {
            const resultDiv = document.getElementById('payrollResult');
            try {
                const payrollManager = new PayrollManager();
                const testEmployee = Database.getEmployee('test_emp_001');

                if (!testEmployee) {
                    setupTestData();
                }

                // اختبار إنشاء كشف راتب مبسط
                const attendanceData = payrollManager.getEmployeeAttendance('test_emp_001', '2024-01');
                const fullSalary = parseFloat(testEmployee.salary) || 0;
                const earnedSalary = payrollManager.calculateEarnedSalary(fullSalary, attendanceData);
                const allowances = payrollManager.calculateAllowances(testEmployee, attendanceData);
                const deductions = payrollManager.calculateDeductions(testEmployee, earnedSalary, '2024-01');

                const netSalary = Math.max(0, earnedSalary + allowances - deductions);

                let result = `<div class="test-result test-pass">`;
                result += `<strong>كشف الراتب المبسط:</strong><br>`;
                result += `الراتب الأساسي الكامل: ${fullSalary.toFixed(2)}<br>`;
                result += `الراتب المستحق (حسب الحضور): ${earnedSalary.toFixed(2)}<br>`;
                result += `البدلات: ${allowances.toFixed(2)}<br>`;
                result += `الخصومات: ${deductions.toFixed(2)}<br>`;
                result += `صافي الراتب: ${netSalary.toFixed(2)}<br>`;
                result += `أيام الحضور: ${attendanceData.actualDays}/${attendanceData.totalDays}<br>`;
                result += `نسبة الحضور: ${attendanceData.percentage}%`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testNewSettings() {
            const resultDiv = document.getElementById('settingsResult');
            try {
                const settings = Database.getSettings();

                let result = `<div class="test-result test-pass">`;
                result += `<strong>الإعدادات الجديدة:</strong><br>`;
                result += `عدد أيام العمل في الشهر: ${settings.working_hours.working_days_per_month || 22}<br>`;
                result += `التأمينات الاجتماعية مفعلة: ${settings.payroll.social_insurance_enabled ? 'نعم' : 'لا'}<br>`;
                result += `معدل التأمينات: ${((settings.payroll.social_insurance_rate || 0) * 100).toFixed(2)}%<br>`;
                result += `ضريبة الدخل مفعلة: ${settings.payroll.income_tax_enabled ? 'نعم' : 'لا'}<br>`;
                result += `الحد الأدنى للضريبة: ${settings.payroll.tax_threshold || 0}<br>`;
                result += `معدل ساعات التأخير: ${settings.attendance.late_penalty_rate || 1}x`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testPenaltyHours() {
            const resultDiv = document.getElementById('penaltyResult');
            try {
                // إنشاء بيانات اختبار للحضور
                const testAttendance = {
                    employee_id: 'test_emp_001',
                    date: '2024-01-15',
                    check_in: '08:30', // تأخير 30 دقيقة
                    check_out: '16:30', // انصراف مبكر 30 دقيقة
                    status: 'late_early_leave'
                };

                // حساب ساعات الخصم
                const settings = Database.getSettings();
                const penaltyRate = settings.attendance?.late_penalty_rate || 1;
                const lateMinutes = 30; // 30 دقيقة تأخير
                const earlyLeaveMinutes = 30; // 30 دقيقة انصراف مبكر
                const totalPenaltyMinutes = (lateMinutes + earlyLeaveMinutes) * penaltyRate;
                const penaltyHours = totalPenaltyMinutes / 60;

                let result = `<div class="test-result test-pass">`;
                result += `<strong>نتيجة حساب ساعات الخصم:</strong><br>`;
                result += `دقائق التأخير: ${lateMinutes}<br>`;
                result += `دقائق الانصراف المبكر: ${earlyLeaveMinutes}<br>`;
                result += `معدل الخصم: ${penaltyRate}x<br>`;
                result += `إجمالي دقائق الخصم: ${totalPenaltyMinutes}<br>`;
                result += `ساعات الخصم النهائية: ${penaltyHours.toFixed(2)} ساعة`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testDetailedCalculations() {
            const resultDiv = document.getElementById('detailsResult');
            try {
                const payrollManager = new PayrollManager();
                const testEmployee = Database.getEmployee('test_emp_001');

                if (!testEmployee) {
                    setupTestData();
                }

                // اختبار حساب تفاصيل البدلات والخصومات
                const attendanceData = payrollManager.getEmployeeAttendance('test_emp_001', '2024-01');
                const allowancesDetails = payrollManager.calculateDetailedAllowances(testEmployee, attendanceData);
                const earnedSalary = payrollManager.calculateEarnedSalary(parseFloat(testEmployee.salary), attendanceData);
                const deductionsDetails = payrollManager.calculateDetailedDeductions(testEmployee, earnedSalary, '2024-01');

                let result = `<div class="test-result test-pass">`;
                result += `<strong>تفاصيل البدلات:</strong><br>`;
                result += `بدل السكن: ${allowancesDetails.housing.toFixed(2)}<br>`;
                result += `بدل المواصلات: ${allowancesDetails.transport.toFixed(2)}<br>`;
                result += `بدل الطعام: ${allowancesDetails.food.toFixed(2)}<br>`;
                result += `المكافآت: ${allowancesDetails.bonus.toFixed(2)}<br>`;
                result += `الحوافز: ${allowancesDetails.incentives.toFixed(2)}<br>`;
                result += `إضافات أخرى: ${allowancesDetails.other.toFixed(2)}<br><br>`;

                result += `<strong>تفاصيل الخصومات:</strong><br>`;
                result += `التأمينات الاجتماعية: ${deductionsDetails.socialInsurance.toFixed(2)}<br>`;
                result += `ضريبة الدخل: ${deductionsDetails.incomeTax.toFixed(2)}<br>`;
                result += `خصومات الحضور: ${deductionsDetails.attendanceDeductions.toFixed(2)}<br>`;
                result += `السلف: ${deductionsDetails.advances.toFixed(2)}<br>`;
                result += `الجزاءات: ${deductionsDetails.penalties.toFixed(2)}<br>`;
                result += `خصومات أخرى: ${deductionsDetails.other.toFixed(2)}`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        function testComprehensiveDeductions() {
            const resultDiv = document.getElementById('comprehensiveResult');
            try {
                const payrollManager = new PayrollManager();
                const testEmployee = Database.getEmployee('test_emp_001');

                if (!testEmployee) {
                    setupTestData();
                }

                // إنشاء بيانات اختبار للإذونات والسلف والجزاءات

                // إذن موافق عليه يؤثر على الراتب
                const testLeave = {
                    id: 'test_leave_001',
                    employee_id: 'test_emp_001',
                    type: 'sick',
                    start_date: '2024-01-15',
                    end_date: '2024-01-16',
                    days: 2,
                    status: 'approved',
                    affects_salary: true
                };
                Database.create('leaves', testLeave);

                // سلفة موافق عليها
                const testAdvance = {
                    id: 'test_advance_001',
                    employee_id: 'test_emp_001',
                    amount: 1000,
                    monthly_installment: 200,
                    remaining_amount: 800,
                    status: 'approved'
                };
                Database.create('advances', testAdvance);

                // جزاء مالي موافق عليه
                const testPenalty = {
                    id: 'test_penalty_001',
                    employee_id: 'test_emp_001',
                    type: 'financial',
                    amount: 100,
                    month: '2024-01',
                    status: 'approved'
                };
                Database.create('penalties', testPenalty);

                // اختبار حساب الخصومات الشاملة
                const comprehensiveDeductions = payrollManager.calculateAttendanceDeductions('test_emp_001', '2024-01');

                let result = `<div class="test-result test-pass">`;
                result += `<strong>نتيجة الخصومات الشاملة:</strong><br>`;
                result += `خصومات التأخير والغياب: محسوبة<br>`;
                result += `خصم الإذونات (2 أيام): محسوب<br>`;
                result += `خصم السلفة (200 ريال): محسوب<br>`;
                result += `خصم الجزاء المالي (100 ريال): محسوب<br>`;
                result += `<strong>إجمالي الخصومات الشاملة: ${comprehensiveDeductions.toFixed(2)} ريال</strong>`;
                result += `</div>`;

                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result test-fail">خطأ: ${error.message}</div>`;
            }
        }

        // تشغيل جميع الاختبارات عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('نظام الرواتب المطور جاهز للاختبار');
        });
    </script>
</body>
</html>
