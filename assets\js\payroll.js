/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payroll Management Module
 * وحدة إدارة الرواتب المبسطة
 */

class PayrollManager {
    constructor() {
        this.currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
        this.selectedEmployee = '';
        this.selectedStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();

        // إضافة تأثير الانتقال
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadPayrollData();
    }

    getMainHTML() {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-money-bill-wave me-2"></i>إدارة الرواتب</h2>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" id="addPayrollBtn">
                                    <i class="fas fa-plus me-2"></i>إضافة راتب
                                </button>
                                <button type="button" class="btn btn-success" id="generateMonthlyBtn">
                                    <i class="fas fa-calendar-plus me-2"></i>إنشاء رواتب شهرية
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">الشهر</label>
                                        <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الموظف</label>
                                        <select class="form-select" id="employeeFilter">
                                            <option value="">جميع الموظفين</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending">في الانتظار</option>
                                            <option value="paid">مدفوع</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-primary w-100" id="filterBtn">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الرواتب -->
                        <div class="row mb-4" id="payrollStats">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>

                        <!-- جدول الرواتب -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">كشوف الرواتب</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="payrollTable">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الشهر</th>
                                                <th>الراتب الأساسي</th>
                                                <th>البدلات</th>
                                                <th>الخصومات</th>
                                                <th>صافي الراتب</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل الراتب -->
            <div class="modal fade" id="payrollModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة راتب جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الشهر *</label>
                                        <input type="month" class="form-control" name="month" required>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="basic_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إجمالي البدلات</label>
                                        <input type="number" class="form-control" name="total_allowances" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إجمالي الخصومات</label>
                                        <input type="number" class="form-control" name="total_deductions" step="0.01" readonly>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الراتب الإجمالي</label>
                                        <input type="number" class="form-control" name="gross_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">صافي الراتب</label>
                                        <input type="number" class="form-control" name="net_salary" step="0.01" readonly>
                                    </div>
                                </div>

                                <!-- تفاصيل البدلات والخصومات -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="accordion" id="payrollDetailsAccordion">
                                            <!-- تفاصيل البدلات -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="allowancesHeading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#allowancesCollapse">
                                                        تفاصيل البدلات
                                                    </button>
                                                </h2>
                                                <div id="allowancesCollapse" class="accordion-collapse collapse" data-bs-parent="#payrollDetailsAccordion">
                                                    <div class="accordion-body">
                                                        <div id="allowancesDetails" class="table-responsive">
                                                            <!-- سيتم ملء التفاصيل هنا -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- تفاصيل الخصومات -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="deductionsHeading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#deductionsCollapse">
                                                        تفاصيل الخصومات
                                                    </button>
                                                </h2>
                                                <div id="deductionsCollapse" class="accordion-collapse collapse" data-bs-parent="#payrollDetailsAccordion">
                                                    <div class="accordion-body">
                                                        <div id="deductionsDetails" class="table-responsive">
                                                            <!-- سيتم ملء التفاصيل هنا -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="savePayrollBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // ربط الأحداث
        document.getElementById('addPayrollBtn').addEventListener('click', () => this.showPayrollModal());
        document.getElementById('generateMonthlyBtn').addEventListener('click', () => this.generateMonthlyPayroll());
        document.getElementById('filterBtn').addEventListener('click', () => this.loadPayrollData());
        document.getElementById('savePayrollBtn').addEventListener('click', () => this.savePayroll());
        
        // تحديث البيانات عند تغيير الموظف
        const employeeSelect = document.querySelector('#payrollForm select[name="employee_id"]');
        if (employeeSelect) {
            employeeSelect.addEventListener('change', () => this.loadEmployeeData());
        }

        // تحديث الفلاتر
        document.getElementById('monthFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('employeeFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('statusFilter').addEventListener('change', () => this.loadPayrollData());
    }

    loadPayrollData() {
        try {
            const month = document.getElementById('monthFilter').value || this.currentMonth;
            const employeeId = document.getElementById('employeeFilter').value;
            const status = document.getElementById('statusFilter').value;

            // جلب البيانات
            let payrolls = Database.getAll('payroll') || [];
            const employees = Database.getEmployees();

            // تطبيق الفلاتر
            if (month) {
                payrolls = payrolls.filter(p => p.month === month);
            }
            if (employeeId) {
                payrolls = payrolls.filter(p => p.employee_id === employeeId);
            }
            if (status) {
                payrolls = payrolls.filter(p => p.status === status);
            }

            // تحديث الإحصائيات
            this.updateStats(payrolls);

            // تحديث الجدول
            this.updateTable(payrolls, employees);

            // تحديث فلاتر الموظفين
            this.updateEmployeeFilters(employees);

        } catch (error) {
            console.error('خطأ في تحميل بيانات الرواتب:', error);
            window.samApp.showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    }

    updateStats(payrolls) {
        const stats = {
            total: payrolls.length,
            totalBasic: payrolls.reduce((sum, p) => sum + (parseFloat(p.basic_salary) || 0), 0),
            totalAllowances: payrolls.reduce((sum, p) => sum + (parseFloat(p.total_allowances) || 0), 0),
            totalDeductions: payrolls.reduce((sum, p) => sum + (parseFloat(p.total_deductions) || 0), 0),
            totalNet: payrolls.reduce((sum, p) => sum + (parseFloat(p.net_salary) || 0), 0),
            totalGross: payrolls.reduce((sum, p) => sum + (parseFloat(p.gross_salary) || 0), 0)
        };

        // حساب متوسط أيام الحضور للموظفين في هذا الشهر
        let totalWorkingDays = 0;
        let employeeCount = 0;

        payrolls.forEach(payroll => {
            if (payroll.month && payroll.employee_id) {
                const workingDays = this.getActualWorkingDays(payroll.employee_id, payroll.month);
                totalWorkingDays += workingDays;
                employeeCount++;
            }
        });

        const averageWorkingDays = employeeCount > 0 ? (totalWorkingDays / employeeCount).toFixed(1) : 0;

        document.getElementById('payrollStats').innerHTML = `
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">${stats.total}</h4>
                        <small>عدد الموظفين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-secondary">${averageWorkingDays}</h4>
                        <small>متوسط أيام الحضور</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info">${window.samApp.formatCurrency(stats.totalBasic)}</h4>
                        <small>إجمالي الرواتب الأساسية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">${window.samApp.formatCurrency(stats.totalAllowances)}</h4>
                        <small>إجمالي البدلات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning">${window.samApp.formatCurrency(stats.totalDeductions)}</h4>
                        <small>إجمالي الخصومات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">${window.samApp.formatCurrency(stats.totalNet)}</h4>
                        <small>صافي الرواتب</small>
                    </div>
                </div>
            </div>
        `;
    }

    updateTable(payrolls, employees) {
        const tbody = document.querySelector('#payrollTable tbody');

        if (payrolls.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">لا توجد كشوف رواتب</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const statusBadge = this.getStatusBadge(payroll.status);

            return `
                <tr>
                    <td>${employee?.name || 'غير معروف'}</td>
                    <td>${payroll.month}</td>
                    <td>${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                    <td>${window.samApp.formatCurrency(payroll.total_allowances || 0)}</td>
                    <td>${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                    <td class="fw-bold">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="payrollManager.editPayroll('${payroll.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="payrollManager.markAsPaid('${payroll.id}')" title="تحديد كمدفوع">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="payrollManager.deletePayroll('${payroll.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'paid': '<span class="badge bg-success">مدفوع</span>',
            'cancelled': '<span class="badge bg-danger">ملغي</span>'
        };
        return statusMap[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    updateEmployeeFilters(employees) {
        const employeeFilter = document.getElementById('employeeFilter');
        const modalEmployeeSelect = document.querySelector('#payrollForm select[name="employee_id"]');

        const options = employees.filter(emp => emp.status === 'active').map(emp =>
            `<option value="${emp.id}">${emp.name} - ${emp.employee_number || ''}</option>`
        ).join('');

        if (employeeFilter) {
            employeeFilter.innerHTML = '<option value="">جميع الموظفين</option>' + options;
        }

        if (modalEmployeeSelect) {
            modalEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>' + options;
        }
    }

    showPayrollModal(payrollId = null) {
        const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
        const form = document.getElementById('payrollForm');
        const title = document.querySelector('#payrollModal .modal-title');

        form.reset();

        if (payrollId) {
            title.textContent = 'تعديل الراتب';
            this.loadPayrollForEdit(payrollId);
        } else {
            title.textContent = 'إضافة راتب جديد';
            document.querySelector('#payrollForm input[name="month"]').value = this.currentMonth;
        }

        modal.show();
    }

    loadEmployeeData() {
        const employeeId = document.querySelector('#payrollForm select[name="employee_id"]').value;
        if (!employeeId) return;

        const employee = Database.getEmployee(employeeId);
        if (!employee) return;

        const form = document.getElementById('payrollForm');
        const month = form.querySelector('input[name="month"]').value;

        // الراتب الأساسي بناءً على أيام الحضور الفعلية
        let basicSalary;
        if (month) {
            basicSalary = this.calculateProportionalBasicSalary(employee, month);
        } else {
            basicSalary = parseFloat(employee.salary) || 0;
        }
        form.querySelector('input[name="basic_salary"]').value = basicSalary;

        // حساب البدلات مع مراعاة أيام الحضور الفعلية
        const totalAllowances = this.calculateSimpleAllowances(employee, month);
        form.querySelector('input[name="total_allowances"]').value = totalAllowances;

        // حساب الخصومات
        const totalDeductions = this.calculateSimpleDeductions(employee, basicSalary, month);
        form.querySelector('input[name="total_deductions"]').value = totalDeductions;

        // حساب الإجماليات
        const grossSalary = basicSalary + totalAllowances;
        const netSalary = Math.max(0, grossSalary - totalDeductions);

        form.querySelector('input[name="gross_salary"]').value = grossSalary;
        form.querySelector('input[name="net_salary"]').value = netSalary;

        // تحديث تفاصيل البدلات والخصومات
        this.updateAllowancesDetails(employee, month);
        this.updateDeductionsDetails(employee, basicSalary, month);
    }

    // دالة لإنشاء تفاصيل البدلات
    updateAllowancesDetails(employee, month = null) {
        const allowancesContainer = document.getElementById('allowancesDetails');
        if (!allowancesContainer) return;

        const allowances = [];
        const basicSalary = parseFloat(employee.salary) || 0;

        // حساب أيام العمل الفعلية
        let workingDays = 22;
        if (month && employee.id) {
            workingDays = this.getActualWorkingDays(employee.id, month);
        }

        // بدل السكن
        if (employee.housing_allowance_type === 'fixed') {
            const amount = parseFloat(employee.housing_allowance) || 0;
            if (amount > 0) {
                allowances.push({
                    type: 'بدل السكن',
                    description: 'بدل ثابت',
                    amount: amount
                });
            }
        } else if (employee.housing_allowance_type === 'percentage') {
            const percentage = parseFloat(employee.housing_allowance_percentage) || 0;
            const amount = basicSalary * (percentage / 100);
            if (amount > 0) {
                allowances.push({
                    type: 'بدل السكن',
                    description: `${percentage}% من الراتب الأساسي`,
                    amount: amount
                });
            }
        }

        // بدل النقل
        if (employee.transport_allowance_type === 'fixed') {
            const amount = parseFloat(employee.transport_allowance) || 0;
            if (amount > 0) {
                allowances.push({
                    type: 'بدل النقل',
                    description: 'بدل ثابت',
                    amount: amount
                });
            }
        } else if (employee.transport_allowance_type === 'daily') {
            const dailyAmount = parseFloat(employee.daily_transport_allowance) || 0;
            const amount = dailyAmount * workingDays;
            if (amount > 0) {
                allowances.push({
                    type: 'بدل النقل',
                    description: `${dailyAmount} × ${workingDays} يوم عمل`,
                    amount: amount
                });
            }
        }

        // بدل الطعام
        if (employee.food_allowance_type === 'fixed') {
            const amount = parseFloat(employee.food_allowance) || 0;
            if (amount > 0) {
                allowances.push({
                    type: 'بدل الطعام',
                    description: 'بدل ثابت',
                    amount: amount
                });
            }
        } else if (employee.food_allowance_type === 'daily') {
            const dailyAmount = parseFloat(employee.daily_food_allowance) || 0;
            const amount = dailyAmount * workingDays;
            if (amount > 0) {
                allowances.push({
                    type: 'بدل الطعام',
                    description: `${dailyAmount} × ${workingDays} يوم عمل`,
                    amount: amount
                });
            }
        }

        // بدلات أخرى
        const otherAllowances = parseFloat(employee.other_allowances) || 0;
        if (otherAllowances > 0) {
            allowances.push({
                type: 'بدلات أخرى',
                description: 'بدلات متنوعة',
                amount: otherAllowances
            });
        }

        // إنشاء الجدول
        if (allowances.length === 0) {
            allowancesContainer.innerHTML = '<p class="text-muted text-center">لا توجد بدلات</p>';
        } else {
            const total = allowances.reduce((sum, item) => sum + item.amount, 0);
            allowancesContainer.innerHTML = `
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>نوع البدل</th>
                            <th>التفاصيل</th>
                            <th class="text-end">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${allowances.map(item => `
                            <tr>
                                <td>${item.type}</td>
                                <td>${item.description}</td>
                                <td class="text-end">${window.samApp.formatCurrency(item.amount)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td colspan="2">الإجمالي</td>
                            <td class="text-end">${window.samApp.formatCurrency(total)}</td>
                        </tr>
                    </tfoot>
                </table>
            `;
        }
    }

    // دالة لإنشاء تفاصيل الخصومات
    updateDeductionsDetails(employee, basicSalary, month = null) {
        const deductionsContainer = document.getElementById('deductionsDetails');
        if (!deductionsContainer) return;

        const deductions = [];
        const grossSalary = basicSalary + this.calculateSimpleAllowances(employee, month);

        // التأمينات الاجتماعية
        const socialInsuranceRate = 0.09;
        const socialInsurance = basicSalary * socialInsuranceRate;
        if (socialInsurance > 0) {
            deductions.push({
                type: 'التأمينات الاجتماعية',
                description: `${(socialInsuranceRate * 100).toFixed(1)}% من الراتب الأساسي`,
                amount: socialInsurance
            });
        }

        // ضريبة الدخل المتدرجة
        let incomeTax = 0;
        let taxDescription = '';
        if (grossSalary > 3000) {
            if (grossSalary <= 5000) {
                incomeTax = (grossSalary - 3000) * 0.05;
                taxDescription = `5% على المبلغ الزائد عن 3000 (${window.samApp.formatCurrency(grossSalary - 3000)})`;
            } else if (grossSalary <= 10000) {
                incomeTax = (2000 * 0.05) + ((grossSalary - 5000) * 0.10);
                taxDescription = `5% على 2000 + 10% على ${window.samApp.formatCurrency(grossSalary - 5000)}`;
            } else {
                incomeTax = (2000 * 0.05) + (5000 * 0.10) + ((grossSalary - 10000) * 0.15);
                taxDescription = `5% على 2000 + 10% على 5000 + 15% على ${window.samApp.formatCurrency(grossSalary - 10000)}`;
            }

            if (incomeTax > 0) {
                deductions.push({
                    type: 'ضريبة الدخل',
                    description: taxDescription,
                    amount: incomeTax
                });
            }
        }

        // السلف النشطة
        const advances = Database.getAll('advances') || [];
        const activeAdvances = advances.filter(advance =>
            advance.employee_id === employee.id &&
            advance.status === 'approved' &&
            advance.remaining_amount > 0
        );

        activeAdvances.forEach(advance => {
            const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;
            if (monthlyInstallment > 0) {
                deductions.push({
                    type: 'سلفة',
                    description: `${advance.advance_type || 'سلفة'} - قسط شهري (متبقي: ${window.samApp.formatCurrency(advance.remaining_amount)})`,
                    amount: monthlyInstallment
                });
            }
        });

        // الجزاءات المالية
        const currentMonth = month || new Date().toISOString().slice(0, 7);
        const penalties = Database.getAll('penalties') || [];
        const monthPenalties = penalties.filter(penalty =>
            penalty.employee_id === employee.id &&
            penalty.month === currentMonth &&
            penalty.status === 'approved' &&
            penalty.type === 'financial'
        );

        monthPenalties.forEach(penalty => {
            const amount = parseFloat(penalty.amount) || 0;
            if (amount > 0) {
                deductions.push({
                    type: 'جزاء مالي',
                    description: penalty.reason || 'جزاء مالي',
                    amount: amount
                });
            }
        });

        // خصومات أخرى
        const otherDeductions = parseFloat(employee.other_deductions) || 0;
        if (otherDeductions > 0) {
            deductions.push({
                type: 'خصومات أخرى',
                description: 'خصومات متنوعة',
                amount: otherDeductions
            });
        }

        // إنشاء الجدول
        if (deductions.length === 0) {
            deductionsContainer.innerHTML = '<p class="text-muted text-center">لا توجد خصومات</p>';
        } else {
            const total = deductions.reduce((sum, item) => sum + item.amount, 0);
            deductionsContainer.innerHTML = `
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>نوع الخصم</th>
                            <th>التفاصيل</th>
                            <th class="text-end">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${deductions.map(item => `
                            <tr>
                                <td>${item.type}</td>
                                <td>${item.description}</td>
                                <td class="text-end">${window.samApp.formatCurrency(item.amount)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td colspan="2">الإجمالي</td>
                            <td class="text-end">${window.samApp.formatCurrency(total)}</td>
                        </tr>
                    </tfoot>
                </table>
            `;
        }
    }

    // دالة لحساب أيام الحضور الفعلية للموظف في شهر معين
    getActualWorkingDays(employeeId, month) {
        try {
            const attendance = Database.getAll('attendance') || [];
            const monthAttendance = attendance.filter(record =>
                record.employee_id === employeeId &&
                record.date &&
                record.date.startsWith(month)
            );

            // حساب أيام الحضور الفعلية (present, late, early_leave)
            const actualWorkingDays = monthAttendance.filter(record =>
                record.status === 'present' ||
                record.status === 'late' ||
                record.status === 'early_leave'
            ).length;

            console.log(`Employee ${employeeId} worked ${actualWorkingDays} days in ${month}`);
            return actualWorkingDays;

        } catch (error) {
            console.error('خطأ في حساب أيام الحضور الفعلية:', error);
            return 22; // القيمة الافتراضية
        }
    }

    // دالة لحساب الراتب الأساسي بناءً على أيام الحضور الفعلية
    calculateProportionalBasicSalary(employee, month) {
        try {
            const fullSalary = parseFloat(employee.salary) || 0;
            const actualWorkingDays = this.getActualWorkingDays(employee.id, month);
            const expectedWorkingDays = 22; // أيام العمل المتوقعة في الشهر

            // حساب الراتب بناءً على نسبة الحضور
            const proportionalSalary = (fullSalary * actualWorkingDays) / expectedWorkingDays;

            console.log(`Employee ${employee.id}: Full salary: ${fullSalary}, Actual days: ${actualWorkingDays}, Proportional salary: ${proportionalSalary}`);
            return Math.round(proportionalSalary * 100) / 100;

        } catch (error) {
            console.error('خطأ في حساب الراتب النسبي:', error);
            return parseFloat(employee.salary) || 0;
        }
    }

    calculateSimpleAllowances(employee, month = null) {
        let total = 0;
        const basicSalary = parseFloat(employee.salary) || 0;

        try {
            // حساب أيام العمل الفعلية إذا تم تمرير الشهر
            let workingDays = 22; // القيمة الافتراضية
            if (month && employee.id) {
                workingDays = this.getActualWorkingDays(employee.id, month);
            }

            // بدل السكن
            if (employee.housing_allowance_type === 'fixed') {
                const housingAllowance = parseFloat(employee.housing_allowance) || 0;
                total += housingAllowance;
            } else if (employee.housing_allowance_type === 'percentage') {
                const percentage = parseFloat(employee.housing_allowance_percentage) || 0;
                const housingAllowance = basicSalary * (percentage / 100);
                total += housingAllowance;
            }

            // بدل النقل
            if (employee.transport_allowance_type === 'fixed') {
                const transportAllowance = parseFloat(employee.transport_allowance) || 0;
                total += transportAllowance;
            } else if (employee.transport_allowance_type === 'daily') {
                const dailyTransport = parseFloat(employee.daily_transport_allowance) || 0;
                total += dailyTransport * workingDays; // استخدام أيام العمل الفعلية
            }

            // بدل الطعام
            if (employee.food_allowance_type === 'fixed') {
                const foodAllowance = parseFloat(employee.food_allowance) || 0;
                total += foodAllowance;
            } else if (employee.food_allowance_type === 'daily') {
                const dailyFood = parseFloat(employee.daily_food_allowance) || 0;
                total += dailyFood * workingDays; // استخدام أيام العمل الفعلية
            }

            // بدلات أخرى
            const otherAllowances = parseFloat(employee.other_allowances) || 0;
            total += otherAllowances;

            // التأكد من أن النتيجة موجبة
            total = Math.max(0, total);

        } catch (error) {
            console.error('خطأ في حساب البدلات:', error);
            total = 0;
        }

        return Math.round(total * 100) / 100;
    }

    calculateSimpleDeductions(employee, basicSalary, month = null) {
        let total = 0;

        try {
            const grossSalary = basicSalary + this.calculateSimpleAllowances(employee, month);

            // التأمينات الاجتماعية (9% من الراتب الأساسي)
            const socialInsuranceRate = 0.09; // 9%
            const socialInsurance = basicSalary * socialInsuranceRate;
            total += socialInsurance;

            // ضريبة الدخل المتدرجة
            let incomeTax = 0;
            if (grossSalary > 3000) {
                if (grossSalary <= 5000) {
                    // 5% للشريحة من 3001 إلى 5000
                    incomeTax = (grossSalary - 3000) * 0.05;
                } else if (grossSalary <= 10000) {
                    // 5% للشريحة الأولى + 10% للشريحة الثانية
                    incomeTax = (2000 * 0.05) + ((grossSalary - 5000) * 0.10);
                } else {
                    // 5% + 10% + 15% للشرائح العليا
                    incomeTax = (2000 * 0.05) + (5000 * 0.10) + ((grossSalary - 10000) * 0.15);
                }
            }
            total += incomeTax;

            // السلف والقروض (إذا كانت موجودة)
            const advances = this.getEmployeeAdvances(employee.id);
            total += advances;

            // الجزاءات المالية (إذا كانت موجودة)
            const penalties = this.getEmployeePenalties(employee.id);
            total += penalties;

            // خصومات أخرى
            const otherDeductions = parseFloat(employee.other_deductions) || 0;
            total += otherDeductions;

            // التأكد من أن النتيجة موجبة
            total = Math.max(0, total);

        } catch (error) {
            console.error('خطأ في حساب الخصومات:', error);
            total = 0;
        }

        return Math.round(total * 100) / 100;
    }

    // دالة مساعدة لحساب السلف النشطة
    getEmployeeAdvances(employeeId, month = null) {
        try {
            const advances = Database.getAll('advances') || [];
            const activeAdvances = advances.filter(advance =>
                advance.employee_id === employeeId &&
                advance.status === 'approved' &&
                parseFloat(advance.remaining_amount) > 0
            );

            let totalDeduction = 0;

            activeAdvances.forEach(advance => {
                const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;
                const remainingAmount = parseFloat(advance.remaining_amount) || 0;

                // التأكد من أن القسط الشهري لا يتجاوز المبلغ المتبقي
                const actualDeduction = Math.min(monthlyInstallment, remainingAmount);
                totalDeduction += actualDeduction;

                // تحديث المبلغ المتبقي (اختياري - يمكن تنفيذه عند دفع الراتب فعلياً)
                if (month && actualDeduction > 0) {
                    console.log(`Advance ${advance.id}: Deducting ${actualDeduction} from remaining ${remainingAmount}`);
                }
            });

            console.log(`Total advances deduction for employee ${employeeId}: ${totalDeduction}`);
            return totalDeduction;
        } catch (error) {
            console.warn('خطأ في جلب السلف:', error);
            return 0;
        }
    }

    // دالة مساعدة لحساب الجزاءات المالية
    getEmployeePenalties(employeeId) {
        try {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const penalties = Database.getAll('penalties') || [];
            const monthPenalties = penalties.filter(penalty =>
                penalty.employee_id === employeeId &&
                penalty.month === currentMonth &&
                penalty.status === 'approved' &&
                penalty.type === 'financial'
            );

            return monthPenalties.reduce((total, penalty) => {
                return total + (parseFloat(penalty.amount) || 0);
            }, 0);
        } catch (error) {
            console.warn('خطأ في جلب الجزاءات:', error);
            return 0;
        }
    }

    savePayroll() {
        try {
            const form = document.getElementById('payrollForm');
            const formData = new FormData(form);
            const payrollId = form.dataset.payrollId;

            const payrollData = {
                employee_id: formData.get('employee_id'),
                month: formData.get('month'),
                basic_salary: parseFloat(formData.get('basic_salary')) || 0,
                total_allowances: parseFloat(formData.get('total_allowances')) || 0,
                total_deductions: parseFloat(formData.get('total_deductions')) || 0,
                gross_salary: parseFloat(formData.get('gross_salary')) || 0,
                net_salary: parseFloat(formData.get('net_salary')) || 0,
                notes: formData.get('notes') || '',
                updated_at: new Date().toISOString()
            };

            // التحقق من البيانات المطلوبة
            if (!payrollData.employee_id || !payrollData.month) {
                window.samApp.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            if (payrollId) {
                // تحديث راتب موجود
                const existingPayroll = Database.getById('payroll', payrollId);
                if (!existingPayroll) {
                    window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                    return;
                }

                // الاحتفاظ ببعض البيانات الأصلية
                payrollData.status = existingPayroll.status;
                payrollData.created_at = existingPayroll.created_at;
                payrollData.payment_date = existingPayroll.payment_date;

                Database.update('payroll', payrollId, payrollData);
                window.samApp.showAlert('تم تحديث الراتب بنجاح', 'success');

            } else {
                // إنشاء راتب جديد

                // التحقق من عدم وجود راتب مكرر
                const existingPayroll = Database.getAll('payroll').find(p =>
                    p.employee_id === payrollData.employee_id && p.month === payrollData.month
                );

                if (existingPayroll) {
                    window.samApp.showAlert('يوجد راتب مسجل لهذا الموظف في هذا الشهر', 'warning');
                    return;
                }

                payrollData.status = 'pending';
                payrollData.created_at = new Date().toISOString();

                Database.create('payroll', payrollData);
                window.samApp.showAlert('تم حفظ الراتب بنجاح', 'success');
            }

            // إغلاق النموذج وتحديث البيانات
            bootstrap.Modal.getInstance(document.getElementById('payrollModal')).hide();
            this.loadPayrollData();

            // مسح معرف الراتب من النموذج
            delete form.dataset.payrollId;

        } catch (error) {
            console.error('خطأ في حفظ الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حفظ الراتب: ' + error.message, 'danger');
        }
    }

    generateMonthlyPayroll() {
        const month = prompt('أدخل الشهر (YYYY-MM):', this.currentMonth);
        if (!month) return;

        // التحقق من تنسيق الشهر
        if (!/^\d{4}-\d{2}$/.test(month)) {
            window.samApp.showAlert('تنسيق الشهر غير صحيح. يجب أن يكون بالتنسيق YYYY-MM', 'danger');
            return;
        }

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        if (employees.length === 0) {
            window.samApp.showAlert('لا يوجد موظفون نشطون لإنشاء كشوف رواتب', 'warning');
            return;
        }

        const existingPayrolls = Database.getAll('payroll').filter(p => p.month === month);

        if (existingPayrolls.length > 0) {
            if (!confirm(`يوجد ${existingPayrolls.length} كشف راتب لهذا الشهر. هل تريد المتابعة وإنشاء كشوف للموظفين المتبقين؟`)) {
                return;
            }
        }

        let created = 0;
        let skipped = 0;

        employees.forEach(employee => {
            // التحقق من عدم وجود راتب مسبق
            const existing = existingPayrolls.find(p => p.employee_id === employee.id);
            if (existing) {
                skipped++;
                return;
            }

            try {
                // حساب الراتب الأساسي بناءً على أيام الحضور الفعلية
                const basicSalary = this.calculateProportionalBasicSalary(employee, month);
                const totalAllowances = this.calculateSimpleAllowances(employee, month);
                const totalDeductions = this.calculateSimpleDeductions(employee, basicSalary, month);
                const grossSalary = basicSalary + totalAllowances;
                const netSalary = Math.max(0, grossSalary - totalDeductions);

                const payrollData = {
                    employee_id: employee.id,
                    month: month,
                    basic_salary: basicSalary,
                    total_allowances: totalAllowances,
                    total_deductions: totalDeductions,
                    gross_salary: grossSalary,
                    net_salary: netSalary,
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                Database.create('payroll', payrollData);
                created++;

            } catch (error) {
                console.error(`خطأ في إنشاء راتب للموظف ${employee.name}:`, error);
            }
        });

        window.samApp.showAlert(`تم إنشاء ${created} كشف راتب جديد. تم تخطي ${skipped} موظف لوجود كشوف رواتب مسبقة.`, 'success');
        this.loadPayrollData();
    }

    editPayroll(payrollId) {
        this.showPayrollModal(payrollId);
    }

    loadPayrollForEdit(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        const form = document.getElementById('payrollForm');

        // ملء البيانات
        form.querySelector('select[name="employee_id"]').value = payroll.employee_id;
        form.querySelector('input[name="month"]').value = payroll.month;
        form.querySelector('input[name="basic_salary"]').value = payroll.basic_salary || 0;
        form.querySelector('input[name="total_allowances"]').value = payroll.total_allowances || 0;
        form.querySelector('input[name="total_deductions"]').value = payroll.total_deductions || 0;
        form.querySelector('input[name="gross_salary"]').value = payroll.gross_salary || 0;
        form.querySelector('input[name="net_salary"]').value = payroll.net_salary || 0;
        form.querySelector('textarea[name="notes"]').value = payroll.notes || '';

        // حفظ معرف الراتب للتحديث
        form.dataset.payrollId = payrollId;
    }

    markAsPaid(payrollId) {
        if (!confirm('هل أنت متأكد من تحديد هذا الراتب كمدفوع؟')) {
            return;
        }

        try {
            const payroll = Database.getById('payroll', payrollId);
            if (!payroll) {
                window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                return;
            }

            // تحديث السلف النشطة عند دفع الراتب
            this.updateAdvancesAfterPayment(payroll.employee_id, payroll.month);

            const updatedData = {
                ...payroll,
                status: 'paid',
                payment_date: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString()
            };

            Database.update('payroll', payrollId, updatedData);
            window.samApp.showAlert('تم تحديد الراتب كمدفوع بنجاح وتم تحديث السلف', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في تحديث حالة الراتب:', error);
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    // دالة لتحديث السلف بعد دفع الراتب
    updateAdvancesAfterPayment(employeeId, month) {
        try {
            const advances = Database.getAll('advances') || [];
            const activeAdvances = advances.filter(advance =>
                advance.employee_id === employeeId &&
                advance.status === 'approved' &&
                parseFloat(advance.remaining_amount) > 0
            );

            activeAdvances.forEach(advance => {
                const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;
                const remainingAmount = parseFloat(advance.remaining_amount) || 0;

                // حساب المبلغ المخصوم فعلياً
                const actualDeduction = Math.min(monthlyInstallment, remainingAmount);
                const newRemainingAmount = Math.max(0, remainingAmount - actualDeduction);

                // تحديث السلفة
                const updatedAdvance = {
                    ...advance,
                    remaining_amount: newRemainingAmount,
                    updated_at: new Date().toISOString()
                };

                // إذا تم سداد السلفة بالكامل، تغيير الحالة
                if (newRemainingAmount === 0) {
                    updatedAdvance.status = 'completed';
                    updatedAdvance.completion_date = new Date().toISOString().split('T')[0];
                }

                Database.update('advances', advance.id, updatedAdvance);

                console.log(`Updated advance ${advance.id}: Remaining amount: ${newRemainingAmount}`);
            });

        } catch (error) {
            console.error('خطأ في تحديث السلف:', error);
        }
    }

    deletePayroll(payrollId) {
        if (!confirm('هل أنت متأكد من حذف كشف الراتب؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            Database.delete('payroll', payrollId);
            window.samApp.showAlert('تم حذف كشف الراتب بنجاح', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في حذف كشف الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حذف كشف الراتب: ' + error.message, 'danger');
        }
    }
}

// إنشاء مثيل من مدير الرواتب
window.payrollManager = new PayrollManager();
