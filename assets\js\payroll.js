/**
 * SAM - نظام إدارة شؤون الموظفين
 * Simple and Accurate Payroll Management Module
 * وحدة إدارة الرواتب المبسطة والدقيقة
 */

class PayrollManager {
    constructor() {
        this.currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
        this.settings = Database.getSettings();
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadPayrollData();
    }

    getMainHTML() {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-money-bill-wave me-2"></i>إدارة الرواتب</h2>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" id="addPayrollBtn">
                                    <i class="fas fa-plus me-2"></i>إضافة راتب
                                </button>
                                <button type="button" class="btn btn-success" id="generateMonthlyBtn">
                                    <i class="fas fa-calendar-plus me-2"></i>إنشاء رواتب شهرية
                                </button>
                                <button type="button" class="btn btn-info" id="attendanceReportBtn">
                                    <i class="fas fa-chart-line me-2"></i>تقرير الحضور
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">الشهر</label>
                                        <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الموظف</label>
                                        <select class="form-select" id="employeeFilter">
                                            <option value="">جميع الموظفين</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending">في الانتظار</option>
                                            <option value="paid">مدفوع</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-primary w-100" id="filterBtn">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الرواتب -->
                        <div class="row mb-4" id="payrollStats">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>

                        <!-- جدول الرواتب -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">كشوف الرواتب</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="payrollTable">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الشهر</th>
                                                <th>أيام الحضور</th>
                                                <th>الراتب المستحق</th>
                                                <th>البدلات</th>
                                                <th>الخصومات</th>
                                                <th>صافي الراتب</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل الراتب -->
            <div class="modal fade" id="payrollModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">كشف راتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الشهر *</label>
                                        <input type="month" class="form-control" name="month" required>
                                    </div>
                                </div>

                                <!-- معلومات الحضور -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">معلومات الحضور</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">أيام الحضور الفعلية</label>
                                                <input type="number" class="form-control" name="actual_working_days" readonly>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">أيام العمل الكاملة</label>
                                                <input type="number" class="form-control" name="total_working_days" readonly>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">نسبة الحضور</label>
                                                <input type="text" class="form-control" name="attendance_percentage" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الراتب والحسابات -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">حسابات الراتب</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">الراتب الأساسي الكامل</label>
                                                <input type="number" class="form-control" name="full_basic_salary" step="0.01" readonly>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">الراتب المستحق (حسب الحضور)</label>
                                                <input type="number" class="form-control" name="earned_salary" step="0.01" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفاصيل البدلات والإضافات -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">البدلات والإضافات</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">بدل السكن</label>
                                                <input type="number" class="form-control" name="housing_allowance" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">بدل المواصلات</label>
                                                <input type="number" class="form-control" name="transport_allowance" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">بدل الطعام</label>
                                                <input type="number" class="form-control" name="food_allowance" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-4">
                                                <label class="form-label">مكافآت شهرية</label>
                                                <input type="number" class="form-control" name="monthly_bonus" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">حوافز</label>
                                                <input type="number" class="form-control" name="incentives" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">إضافات أخرى</label>
                                                <input type="number" class="form-control" name="other_allowances" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <label class="form-label fw-bold">إجمالي البدلات</label>
                                                <input type="number" class="form-control fw-bold bg-light" name="total_allowances" step="0.01" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- تفاصيل الخصومات -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">الخصومات</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">التأمينات الاجتماعية</label>
                                                <input type="number" class="form-control" name="social_insurance" step="0.01" readonly>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">ضريبة الدخل</label>
                                                <input type="number" class="form-control" name="income_tax" step="0.01" readonly>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">خصومات الحضور</label>
                                                <input type="number" class="form-control" name="attendance_deductions" step="0.01" readonly>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-4">
                                                <label class="form-label">السلف والقروض</label>
                                                <input type="number" class="form-control" name="advances_deduction" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الجزاءات المالية</label>
                                                <input type="number" class="form-control" name="penalties_deduction" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">خصومات أخرى</label>
                                                <input type="number" class="form-control" name="other_deductions" step="0.01" onchange="payrollManager.recalculatePayroll()">
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <label class="form-label fw-bold">إجمالي الخصومات</label>
                                                <input type="number" class="form-control fw-bold bg-light" name="total_deductions" step="0.01" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- صافي الراتب -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">النتيجة النهائية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <label class="form-label fw-bold text-success">صافي الراتب</label>
                                                <input type="number" class="form-control fw-bold text-success fs-5" name="net_salary" step="0.01" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="savePayrollBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        document.getElementById('addPayrollBtn').addEventListener('click', () => this.showPayrollModal());
        document.getElementById('generateMonthlyBtn').addEventListener('click', () => this.generateMonthlyPayroll());
        document.getElementById('filterBtn').addEventListener('click', () => this.loadPayrollData());
        document.getElementById('savePayrollBtn').addEventListener('click', () => this.savePayroll());
        document.getElementById('attendanceReportBtn').addEventListener('click', () => this.showAttendanceReport());

        // تحديث البيانات عند تغيير الموظف
        const employeeSelect = document.querySelector('#payrollForm select[name="employee_id"]');
        if (employeeSelect) {
            employeeSelect.addEventListener('change', () => this.calculatePayroll());
        }

        // تحديث الفلاتر
        document.getElementById('monthFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('employeeFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('statusFilter').addEventListener('change', () => this.loadPayrollData());
    }

    /**
     * تحميل بيانات الرواتب
     */
    loadPayrollData() {
        try {
            const month = document.getElementById('monthFilter').value || this.currentMonth;
            const employeeId = document.getElementById('employeeFilter').value;
            const status = document.getElementById('statusFilter').value;

            // جلب البيانات
            let payrolls = Database.getAll('payroll') || [];
            const employees = Database.getEmployees();

            // تطبيق الفلاتر
            if (month) {
                payrolls = payrolls.filter(p => p.month === month);
            }
            if (employeeId) {
                payrolls = payrolls.filter(p => p.employee_id === employeeId);
            }
            if (status) {
                payrolls = payrolls.filter(p => p.status === status);
            }

            this.updateTable(payrolls, employees);
            this.updateStats(payrolls);
            this.populateEmployeeFilters(employees);

        } catch (error) {
            console.error('خطأ في تحميل بيانات الرواتب:', error);
            window.samApp.showAlert('حدث خطأ في تحميل البيانات: ' + error.message, 'danger');
        }
    }

    /**
     * تحديث جدول الرواتب
     */
    updateTable(payrolls, employees) {
        const tbody = document.querySelector('#payrollTable tbody');

        if (payrolls.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-muted">لا توجد كشوف رواتب</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const statusBadge = this.getStatusBadge(payroll.status);

            return `
                <tr>
                    <td>${employee?.name || 'غير معروف'}</td>
                    <td>${payroll.month}</td>
                    <td class="text-center">${payroll.actual_working_days}/${payroll.total_working_days}</td>
                    <td>${window.samApp.formatCurrency(payroll.earned_salary || 0)}</td>
                    <td>
                        <span class="text-success" title="تفاصيل البدلات">
                            ${window.samApp.formatCurrency(payroll.total_allowances || 0)}
                        </span>
                    </td>
                    <td>
                        <span class="text-danger" title="تفاصيل الخصومات">
                            ${window.samApp.formatCurrency(payroll.total_deductions || 0)}
                        </span>
                    </td>
                    <td class="fw-bold text-primary">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="payrollManager.editPayroll('${payroll.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="payrollManager.markAsPaid('${payroll.id}')" title="تحديد كمدفوع">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="payrollManager.deletePayroll('${payroll.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats(payrolls) {
        const statsContainer = document.getElementById('payrollStats');

        const totalPayrolls = payrolls.length;
        const totalSalaries = payrolls.reduce((sum, p) => sum + (parseFloat(p.net_salary) || 0), 0);
        const paidPayrolls = payrolls.filter(p => p.status === 'paid').length;
        const pendingPayrolls = payrolls.filter(p => p.status === 'pending').length;

        statsContainer.innerHTML = `
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${totalPayrolls}</h4>
                                <p class="mb-0">إجمالي الكشوف</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file-invoice fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${window.samApp.formatCurrency(totalSalaries)}</h4>
                                <p class="mb-0">إجمالي الرواتب</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${paidPayrolls}</h4>
                                <p class="mb-0">مدفوعة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${pendingPayrolls}</h4>
                                <p class="mb-0">في الانتظار</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حساب الراتب بناءً على أيام العمل الفعلية
     */
    calculatePayroll() {
        const employeeId = document.querySelector('#payrollForm select[name="employee_id"]').value;
        const month = document.querySelector('#payrollForm input[name="month"]').value;

        if (!employeeId || !month) return;

        const employee = Database.getEmployee(employeeId);
        if (!employee) return;

        try {
            // حساب أيام الحضور الفعلية
            const attendanceData = this.getEmployeeAttendance(employeeId, month);

            // الراتب الأساسي الكامل
            const fullBasicSalary = parseFloat(employee.salary) || 0;

            // حساب الراتب المستحق بناءً على أيام الحضور الفعلية
            const earnedSalary = this.calculateEarnedSalary(fullBasicSalary, attendanceData);

            // حساب البدلات
            const totalAllowances = this.calculateAllowances(employee, attendanceData);

            // حساب الخصومات
            const totalDeductions = this.calculateDeductions(employee, earnedSalary, month);

            // صافي الراتب
            const netSalary = Math.max(0, earnedSalary + totalAllowances - totalDeductions);

            // حساب تفاصيل البدلات
            const allowancesDetails = this.calculateDetailedAllowances(employee, attendanceData);

            // حساب تفاصيل الخصومات
            const deductionsDetails = this.calculateDetailedDeductions(employee, earnedSalary, month);

            // ملء النموذج
            this.fillPayrollForm({
                actual_working_days: attendanceData.actualDays,
                total_working_days: attendanceData.totalDays,
                attendance_percentage: attendanceData.percentage + '%',
                full_basic_salary: fullBasicSalary.toFixed(2),
                earned_salary: earnedSalary.toFixed(2),

                // تفاصيل البدلات
                housing_allowance: allowancesDetails.housing.toFixed(2),
                transport_allowance: allowancesDetails.transport.toFixed(2),
                food_allowance: allowancesDetails.food.toFixed(2),
                monthly_bonus: allowancesDetails.bonus.toFixed(2),
                incentives: allowancesDetails.incentives.toFixed(2),
                other_allowances: allowancesDetails.other.toFixed(2),
                total_allowances: totalAllowances.toFixed(2),

                // تفاصيل الخصومات
                social_insurance: deductionsDetails.socialInsurance.toFixed(2),
                income_tax: deductionsDetails.incomeTax.toFixed(2),
                attendance_deductions: deductionsDetails.attendanceDeductions.toFixed(2),
                advances_deduction: deductionsDetails.advances.toFixed(2),
                penalties_deduction: deductionsDetails.penalties.toFixed(2),
                other_deductions: deductionsDetails.other.toFixed(2),
                total_deductions: totalDeductions.toFixed(2),

                net_salary: netSalary.toFixed(2)
            });

        } catch (error) {
            console.error('خطأ في حساب الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حساب الراتب: ' + error.message, 'warning');
        }
    }

    /**
     * حساب أيام الحضور الفعلية للموظف
     */
    getEmployeeAttendance(employeeId, month) {
        const attendance = Database.getAll('attendance') || [];
        const monthAttendance = attendance.filter(record =>
            record.employee_id === employeeId &&
            record.date &&
            record.date.startsWith(month)
        );

        // حساب أيام الحضور الفعلية
        const actualDays = monthAttendance.filter(record =>
            record.status === 'present' ||
            record.status === 'late' ||
            record.status === 'early_leave' ||
            record.status === 'late_early_leave'
        ).length;

        // إجمالي أيام العمل في الشهر من الإعدادات
        const totalDays = this.settings.working_hours.working_days_per_month || 22;

        // حساب نسبة الحضور
        const percentage = totalDays > 0 ? Math.round((actualDays / totalDays) * 100) : 0;

        return {
            actualDays,
            totalDays,
            percentage
        };
    }

    /**
     * حساب الراتب المستحق بناءً على أيام الحضور الفعلية
     */
    calculateEarnedSalary(fullSalary, attendanceData) {
        const { actualDays, totalDays } = attendanceData;

        // الراتب المستحق = (الراتب الكامل × أيام الحضور الفعلية) ÷ إجمالي أيام العمل
        const earnedSalary = (fullSalary * actualDays) / totalDays;

        return Math.round(earnedSalary * 100) / 100;
    }

    /**
     * حساب البدلات
     */
    calculateAllowances(employee, attendanceData) {
        let total = 0;
        const { actualDays, totalDays } = attendanceData;
        const attendanceRatio = actualDays / totalDays;

        // بدل السكن (ثابت)
        total += parseFloat(employee.housing_allowance) || 0;

        // بدل المواصلات (نسبي حسب الحضور)
        const transportAllowance = parseFloat(employee.transport_allowance) || 0;
        total += transportAllowance * attendanceRatio;

        // بدل الطعام (نسبي حسب الحضور)
        const foodAllowance = parseFloat(employee.food_allowance) || 0;
        total += foodAllowance * attendanceRatio;

        // المكافآت والحوافز (ثابتة)
        total += parseFloat(employee.monthly_bonus) || 0;
        total += parseFloat(employee.incentives) || 0;
        total += parseFloat(employee.other_allowances) || 0;

        return Math.round(total * 100) / 100;
    }

    /**
     * حساب الخصومات
     */
    calculateDeductions(employee, earnedSalary, month) {
        let total = 0;
        const payrollSettings = this.settings.payroll || {};

        // التأمينات الاجتماعية (اختيارية)
        if (payrollSettings.social_insurance_enabled && payrollSettings.social_insurance_rate > 0) {
            total += earnedSalary * payrollSettings.social_insurance_rate;
        }

        // ضريبة الدخل (اختيارية)
        if (payrollSettings.income_tax_enabled && payrollSettings.tax_threshold > 0) {
            const grossSalary = earnedSalary + this.calculateAllowances(employee, this.getEmployeeAttendance(employee.id, month));
            if (grossSalary > payrollSettings.tax_threshold) {
                // ضريبة بسيطة 5% على ما يزيد عن الحد الأدنى
                total += (grossSalary - payrollSettings.tax_threshold) * 0.05;
            }
        }

        // السلف والقروض
        total += this.getEmployeeAdvances(employee.id, month);

        // الجزاءات المالية
        total += this.getEmployeePenalties(employee.id, month);

        // خصومات أخرى
        total += parseFloat(employee.other_deductions) || 0;

        return Math.round(total * 100) / 100;
    }

    /**
     * حساب السلف النشطة للموظف
     */
    getEmployeeAdvances(employeeId, month) {
        try {
            const advances = Database.getAll('advances') || [];
            const activeAdvances = advances.filter(advance =>
                advance.employee_id === employeeId &&
                advance.status === 'approved' &&
                parseFloat(advance.remaining_amount) > 0
            );

            let totalDeduction = 0;
            activeAdvances.forEach(advance => {
                const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;
                const remainingAmount = parseFloat(advance.remaining_amount) || 0;
                const actualDeduction = Math.min(monthlyInstallment, remainingAmount);
                totalDeduction += actualDeduction;
            });

            return Math.round(totalDeduction * 100) / 100;
        } catch (error) {
            console.warn('خطأ في جلب السلف:', error);
            return 0;
        }
    }

    /**
     * حساب الجزاءات المالية للموظف
     */
    getEmployeePenalties(employeeId, month) {
        try {
            const penalties = Database.getAll('penalties') || [];
            const monthPenalties = penalties.filter(penalty =>
                penalty.employee_id === employeeId &&
                penalty.month === month &&
                penalty.status === 'approved' &&
                penalty.type === 'financial'
            );

            const total = monthPenalties.reduce((sum, penalty) => {
                return sum + (parseFloat(penalty.amount) || 0);
            }, 0);

            return Math.round(total * 100) / 100;
        } catch (error) {
            console.warn('خطأ في جلب الجزاءات:', error);
            return 0;
        }
    }

    /**
     * حساب تفاصيل البدلات
     */
    calculateDetailedAllowances(employee, attendanceData) {
        const { actualDays, totalDays } = attendanceData;
        const attendanceRatio = actualDays / totalDays;

        return {
            housing: parseFloat(employee.housing_allowance) || 0, // ثابت
            transport: (parseFloat(employee.transport_allowance) || 0) * attendanceRatio, // نسبي
            food: (parseFloat(employee.food_allowance) || 0) * attendanceRatio, // نسبي
            bonus: parseFloat(employee.monthly_bonus) || 0, // ثابت
            incentives: parseFloat(employee.incentives) || 0, // ثابت
            other: parseFloat(employee.other_allowances) || 0 // ثابت
        };
    }

    /**
     * حساب تفاصيل الخصومات
     */
    calculateDetailedDeductions(employee, earnedSalary, month) {
        const payrollSettings = this.settings.payroll || {};

        // التأمينات الاجتماعية
        let socialInsurance = 0;
        if (payrollSettings.social_insurance_enabled && payrollSettings.social_insurance_rate > 0) {
            socialInsurance = earnedSalary * payrollSettings.social_insurance_rate;
        }

        // ضريبة الدخل
        let incomeTax = 0;
        if (payrollSettings.income_tax_enabled && payrollSettings.tax_threshold > 0) {
            const allowances = this.calculateAllowances(employee, this.getEmployeeAttendance(employee.id, month));
            const grossSalary = earnedSalary + allowances;
            if (grossSalary > payrollSettings.tax_threshold) {
                incomeTax = (grossSalary - payrollSettings.tax_threshold) * 0.05;
            }
        }

        // خصومات الحضور (التأخير والغياب)
        const attendanceDeductions = this.calculateAttendanceDeductions(employee.id, month);

        return {
            socialInsurance: Math.round(socialInsurance * 100) / 100,
            incomeTax: Math.round(incomeTax * 100) / 100,
            attendanceDeductions: attendanceDeductions,
            advances: this.getEmployeeAdvances(employee.id, month),
            penalties: this.getEmployeePenalties(employee.id, month),
            other: parseFloat(employee.other_deductions) || 0
        };
    }

    /**
     * حساب خصومات الحضور (التأخير والغياب)
     */
    calculateAttendanceDeductions(employeeId, month) {
        try {
            const attendance = Database.getAll('attendance') || [];
            const monthAttendance = attendance.filter(record =>
                record.employee_id === employeeId &&
                record.date &&
                record.date.startsWith(month)
            );

            let totalPenaltyHours = 0;
            monthAttendance.forEach(record => {
                totalPenaltyHours += parseFloat(record.penalty_hours) || 0;
            });

            // حساب قيمة الخصم بناءً على الراتب الساعي
            const employee = Database.getEmployee(employeeId);
            const fullSalary = parseFloat(employee.salary) || 0;
            const workingDaysPerMonth = this.settings.working_hours.working_days_per_month || 22;
            const workingHoursPerDay = 8;
            const hourlyRate = fullSalary / (workingDaysPerMonth * workingHoursPerDay);

            const deductionAmount = totalPenaltyHours * hourlyRate;

            return Math.round(deductionAmount * 100) / 100;
        } catch (error) {
            console.warn('خطأ في حساب خصومات الحضور:', error);
            return 0;
        }
    }

    /**
     * ملء نموذج الراتب
     */
    fillPayrollForm(data) {
        const form = document.getElementById('payrollForm');
        Object.keys(data).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = data[key];
            }
        });
    }

    /**
     * عرض نموذج الراتب
     */
    showPayrollModal(payrollId = null) {
        const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
        const form = document.getElementById('payrollForm');

        // إعادة تعيين النموذج
        form.reset();
        delete form.dataset.payrollId;

        // تحميل قائمة الموظفين
        this.populateEmployeeSelect();

        if (payrollId) {
            this.loadPayrollForEdit(payrollId);
            document.querySelector('#payrollModal .modal-title').textContent = 'تعديل كشف راتب';
        } else {
            document.querySelector('#payrollModal .modal-title').textContent = 'إضافة كشف راتب جديد';
            // تعيين الشهر الحالي
            form.querySelector('input[name="month"]').value = this.currentMonth;
        }

        modal.show();
    }

    /**
     * تحميل قائمة الموظفين
     */
    populateEmployeeSelect() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const select = document.querySelector('#payrollForm select[name="employee_id"]');

        select.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            select.innerHTML += `<option value="${employee.id}">${employee.name} - ${employee.employee_number}</option>`;
        });
    }

    /**
     * تحميل فلاتر الموظفين
     */
    populateEmployeeFilters(employees) {
        const select = document.getElementById('employeeFilter');
        const currentValue = select.value;

        select.innerHTML = '<option value="">جميع الموظفين</option>';
        employees.forEach(employee => {
            const selected = employee.id === currentValue ? 'selected' : '';
            select.innerHTML += `<option value="${employee.id}" ${selected}>${employee.name}</option>`;
        });
    }

    /**
     * الحصول على شارة الحالة
     */
    getStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'paid': '<span class="badge bg-success">مدفوع</span>',
            'cancelled': '<span class="badge bg-danger">ملغي</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    /**
     * إعادة حساب الراتب عند تعديل البدلات أو الخصومات
     */
    recalculatePayroll() {
        const form = document.getElementById('payrollForm');
        if (!form) return;

        try {
            // الحصول على الراتب المستحق
            const earnedSalary = parseFloat(form.querySelector('input[name="earned_salary"]').value) || 0;

            // حساب إجمالي البدلات
            const housingAllowance = parseFloat(form.querySelector('input[name="housing_allowance"]').value) || 0;
            const transportAllowance = parseFloat(form.querySelector('input[name="transport_allowance"]').value) || 0;
            const foodAllowance = parseFloat(form.querySelector('input[name="food_allowance"]').value) || 0;
            const monthlyBonus = parseFloat(form.querySelector('input[name="monthly_bonus"]').value) || 0;
            const incentives = parseFloat(form.querySelector('input[name="incentives"]').value) || 0;
            const otherAllowances = parseFloat(form.querySelector('input[name="other_allowances"]').value) || 0;

            const totalAllowances = housingAllowance + transportAllowance + foodAllowance + monthlyBonus + incentives + otherAllowances;

            // حساب إجمالي الخصومات
            const socialInsurance = parseFloat(form.querySelector('input[name="social_insurance"]').value) || 0;
            const incomeTax = parseFloat(form.querySelector('input[name="income_tax"]').value) || 0;
            const attendanceDeductions = parseFloat(form.querySelector('input[name="attendance_deductions"]').value) || 0;
            const advancesDeduction = parseFloat(form.querySelector('input[name="advances_deduction"]').value) || 0;
            const penaltiesDeduction = parseFloat(form.querySelector('input[name="penalties_deduction"]').value) || 0;
            const otherDeductions = parseFloat(form.querySelector('input[name="other_deductions"]').value) || 0;

            const totalDeductions = socialInsurance + incomeTax + attendanceDeductions + advancesDeduction + penaltiesDeduction + otherDeductions;

            // حساب صافي الراتب
            const netSalary = Math.max(0, earnedSalary + totalAllowances - totalDeductions);

            // تحديث الحقول
            form.querySelector('input[name="total_allowances"]').value = totalAllowances.toFixed(2);
            form.querySelector('input[name="total_deductions"]').value = totalDeductions.toFixed(2);
            form.querySelector('input[name="net_salary"]').value = netSalary.toFixed(2);

        } catch (error) {
            console.error('خطأ في إعادة حساب الراتب:', error);
        }
    }

    /**
     * حفظ كشف الراتب
     */
    savePayroll() {
        try {
            const form = document.getElementById('payrollForm');
            const formData = new FormData(form);
            const payrollId = form.dataset.payrollId;

            const payrollData = {
                employee_id: formData.get('employee_id'),
                month: formData.get('month'),
                actual_working_days: parseInt(formData.get('actual_working_days')) || 0,
                total_working_days: parseInt(formData.get('total_working_days')) || 0,
                attendance_percentage: parseFloat(formData.get('attendance_percentage')) || 0,
                full_basic_salary: parseFloat(formData.get('full_basic_salary')) || 0,
                earned_salary: parseFloat(formData.get('earned_salary')) || 0,

                // تفاصيل البدلات
                housing_allowance: parseFloat(formData.get('housing_allowance')) || 0,
                transport_allowance: parseFloat(formData.get('transport_allowance')) || 0,
                food_allowance: parseFloat(formData.get('food_allowance')) || 0,
                monthly_bonus: parseFloat(formData.get('monthly_bonus')) || 0,
                incentives: parseFloat(formData.get('incentives')) || 0,
                other_allowances: parseFloat(formData.get('other_allowances')) || 0,
                total_allowances: parseFloat(formData.get('total_allowances')) || 0,

                // تفاصيل الخصومات
                social_insurance: parseFloat(formData.get('social_insurance')) || 0,
                income_tax: parseFloat(formData.get('income_tax')) || 0,
                attendance_deductions: parseFloat(formData.get('attendance_deductions')) || 0,
                advances_deduction: parseFloat(formData.get('advances_deduction')) || 0,
                penalties_deduction: parseFloat(formData.get('penalties_deduction')) || 0,
                other_deductions: parseFloat(formData.get('other_deductions')) || 0,
                total_deductions: parseFloat(formData.get('total_deductions')) || 0,

                net_salary: parseFloat(formData.get('net_salary')) || 0,
                notes: formData.get('notes') || '',
                updated_at: new Date().toISOString()
            };

            // التحقق من البيانات المطلوبة
            if (!payrollData.employee_id || !payrollData.month) {
                window.samApp.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            if (payrollId) {
                // تحديث راتب موجود
                const existingPayroll = Database.getById('payroll', payrollId);
                if (!existingPayroll) {
                    window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                    return;
                }

                payrollData.status = existingPayroll.status;
                payrollData.created_at = existingPayroll.created_at;
                payrollData.payment_date = existingPayroll.payment_date;

                Database.update('payroll', payrollId, payrollData);
                window.samApp.showAlert('تم تحديث الراتب بنجاح', 'success');

            } else {
                // إنشاء راتب جديد
                const existingPayroll = Database.getAll('payroll').find(p =>
                    p.employee_id === payrollData.employee_id && p.month === payrollData.month
                );

                if (existingPayroll) {
                    window.samApp.showAlert('يوجد راتب مسجل لهذا الموظف في هذا الشهر', 'warning');
                    return;
                }

                payrollData.status = 'pending';
                payrollData.created_at = new Date().toISOString();

                Database.create('payroll', payrollData);
                window.samApp.showAlert('تم حفظ الراتب بنجاح', 'success');
            }

            // إغلاق النموذج وتحديث البيانات
            bootstrap.Modal.getInstance(document.getElementById('payrollModal')).hide();
            this.loadPayrollData();
            delete form.dataset.payrollId;

        } catch (error) {
            console.error('خطأ في حفظ الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حفظ الراتب: ' + error.message, 'danger');
        }
    }

    /**
     * إنشاء رواتب شهرية لجميع الموظفين
     */
    generateMonthlyPayroll() {
        const month = prompt('أدخل الشهر (YYYY-MM):', this.currentMonth);
        if (!month) return;

        if (!/^\d{4}-\d{2}$/.test(month)) {
            window.samApp.showAlert('تنسيق الشهر غير صحيح. يجب أن يكون بالتنسيق YYYY-MM', 'danger');
            return;
        }

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        if (employees.length === 0) {
            window.samApp.showAlert('لا يوجد موظفون نشطون لإنشاء كشوف رواتب', 'warning');
            return;
        }

        const existingPayrolls = Database.getAll('payroll').filter(p => p.month === month);
        if (existingPayrolls.length > 0) {
            if (!confirm(`يوجد ${existingPayrolls.length} كشف راتب لهذا الشهر. هل تريد المتابعة؟`)) {
                return;
            }
        }

        let created = 0;
        let skipped = 0;
        let errors = 0;

        window.samApp.showAlert('جاري إنشاء كشوف الرواتب...', 'info');

        employees.forEach(employee => {
            const existing = existingPayrolls.find(p => p.employee_id === employee.id);
            if (existing) {
                skipped++;
                return;
            }

            try {
                // حساب بيانات الحضور
                const attendanceData = this.getEmployeeAttendance(employee.id, month);

                // حساب الراتب
                const fullBasicSalary = parseFloat(employee.salary) || 0;
                const earnedSalary = this.calculateEarnedSalary(fullBasicSalary, attendanceData);
                const totalAllowances = this.calculateAllowances(employee, attendanceData);
                const totalDeductions = this.calculateDeductions(employee, earnedSalary, month);
                const netSalary = Math.max(0, earnedSalary + totalAllowances - totalDeductions);

                // حساب تفاصيل البدلات والخصومات
                const allowancesDetails = this.calculateDetailedAllowances(employee, attendanceData);
                const deductionsDetails = this.calculateDetailedDeductions(employee, earnedSalary, month);

                const payrollData = {
                    employee_id: employee.id,
                    month: month,
                    actual_working_days: attendanceData.actualDays,
                    total_working_days: attendanceData.totalDays,
                    attendance_percentage: attendanceData.percentage,
                    full_basic_salary: fullBasicSalary,
                    earned_salary: earnedSalary,

                    // تفاصيل البدلات
                    housing_allowance: allowancesDetails.housing,
                    transport_allowance: allowancesDetails.transport,
                    food_allowance: allowancesDetails.food,
                    monthly_bonus: allowancesDetails.bonus,
                    incentives: allowancesDetails.incentives,
                    other_allowances: allowancesDetails.other,
                    total_allowances: totalAllowances,

                    // تفاصيل الخصومات
                    social_insurance: deductionsDetails.socialInsurance,
                    income_tax: deductionsDetails.incomeTax,
                    attendance_deductions: deductionsDetails.attendanceDeductions,
                    advances_deduction: deductionsDetails.advances,
                    penalties_deduction: deductionsDetails.penalties,
                    other_deductions: deductionsDetails.other,
                    total_deductions: totalDeductions,

                    net_salary: netSalary,
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                Database.create('payroll', payrollData);
                created++;

            } catch (error) {
                console.error(`خطأ في إنشاء راتب للموظف ${employee.name}:`, error);
                errors++;
            }
        });

        let message = `تم إنشاء ${created} كشف راتب جديد بنجاح.`;
        if (skipped > 0) message += ` تم تخطي ${skipped} موظف.`;
        if (errors > 0) message += ` حدثت أخطاء في ${errors} كشف راتب.`;

        window.samApp.showAlert(message, errors > 0 ? 'warning' : 'success');
        this.loadPayrollData();
    }

    /**
     * تعديل كشف راتب
     */
    editPayroll(payrollId) {
        this.showPayrollModal(payrollId);
    }

    /**
     * تحميل بيانات الراتب للتعديل
     */
    loadPayrollForEdit(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        const form = document.getElementById('payrollForm');

        // إعادة تحميل قائمة الموظفين أولاً
        this.populateEmployeeSelect();

        // ملء البيانات
        form.querySelector('select[name="employee_id"]').value = payroll.employee_id;
        form.querySelector('input[name="month"]').value = payroll.month;

        this.fillPayrollForm({
            actual_working_days: payroll.actual_working_days || 0,
            total_working_days: payroll.total_working_days || 0,
            attendance_percentage: (payroll.attendance_percentage || 0) + '%',
            full_basic_salary: payroll.full_basic_salary || 0,
            earned_salary: payroll.earned_salary || 0,

            // تفاصيل البدلات
            housing_allowance: payroll.housing_allowance || 0,
            transport_allowance: payroll.transport_allowance || 0,
            food_allowance: payroll.food_allowance || 0,
            monthly_bonus: payroll.monthly_bonus || 0,
            incentives: payroll.incentives || 0,
            other_allowances: payroll.other_allowances || 0,
            total_allowances: payroll.total_allowances || 0,

            // تفاصيل الخصومات
            social_insurance: payroll.social_insurance || 0,
            income_tax: payroll.income_tax || 0,
            attendance_deductions: payroll.attendance_deductions || 0,
            advances_deduction: payroll.advances_deduction || 0,
            penalties_deduction: payroll.penalties_deduction || 0,
            other_deductions: payroll.other_deductions || 0,
            total_deductions: payroll.total_deductions || 0,

            net_salary: payroll.net_salary || 0,
            notes: payroll.notes || ''
        });

        form.dataset.payrollId = payrollId;
    }

    /**
     * تحديد الراتب كمدفوع
     */
    markAsPaid(payrollId) {
        if (!confirm('هل أنت متأكد من تحديد هذا الراتب كمدفوع؟')) {
            return;
        }

        try {
            const payroll = Database.getById('payroll', payrollId);
            if (!payroll) {
                window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                return;
            }

            const updatedData = {
                ...payroll,
                status: 'paid',
                payment_date: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString()
            };

            Database.update('payroll', payrollId, updatedData);
            window.samApp.showAlert('تم تحديد الراتب كمدفوع بنجاح', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في تحديث حالة الراتب:', error);
            window.samApp.showAlert('حدث خطأ في تحديث حالة الراتب: ' + error.message, 'danger');
        }
    }

    /**
     * حذف كشف راتب
     */
    deletePayroll(payrollId) {
        if (!confirm('هل أنت متأكد من حذف كشف الراتب هذا؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            Database.delete('payroll', payrollId);
            window.samApp.showAlert('تم حذف كشف الراتب بنجاح', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في حذف كشف الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حذف كشف الراتب: ' + error.message, 'danger');
        }
    }

    /**
     * عرض تقرير الحضور
     */
    showAttendanceReport() {
        const month = document.getElementById('monthFilter').value || this.currentMonth;
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');

        let reportHTML = `
            <div class="modal fade" id="attendanceReportModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تقرير الحضور - ${month}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>أيام الحضور</th>
                                            <th>إجمالي أيام العمل</th>
                                            <th>نسبة الحضور</th>
                                            <th>الراتب المستحق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;

        employees.forEach(employee => {
            const attendanceData = this.getEmployeeAttendance(employee.id, month);
            const fullSalary = parseFloat(employee.salary) || 0;
            const earnedSalary = this.calculateEarnedSalary(fullSalary, attendanceData);

            reportHTML += `
                <tr>
                    <td>${employee.name}</td>
                    <td class="text-center">${attendanceData.actualDays}</td>
                    <td class="text-center">${attendanceData.totalDays}</td>
                    <td class="text-center">
                        <span class="badge bg-${attendanceData.percentage >= 90 ? 'success' : attendanceData.percentage >= 75 ? 'warning' : 'danger'}">
                            ${attendanceData.percentage}%
                        </span>
                    </td>
                    <td>${window.samApp.formatCurrency(earnedSalary)}</td>
                </tr>
            `;
        });

        reportHTML += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.print()">طباعة</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', reportHTML);
        const modal = new bootstrap.Modal(document.getElementById('attendanceReportModal'));
        modal.show();

        document.getElementById('attendanceReportModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }
}

// تصدير الكلاس للاستخدام العام
window.PayrollManager = PayrollManager;

// متغير عام للمدير
window.payrollManager = null;
